<?php
// Auto-generated blog post
// Source: content\i-prefer-cooking.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'I cook most of my meals';
$meta_description = 'Experiences and strategies for cooking while experiencing housing instability';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'I cook most of my meals',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Experiences and strategies for cooking while experiencing housing instability',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\i-prefer-cooking.md',
);

// Post content
$post_content = '<p>Discovering a preference for home-cooked meals over dining out has been life-changing. While the occasional restaurant visit holds its charm, the act of preparing food for others fosters a unique connection. Sharing a homemade meal allows for a deeper, more meaningful interaction.</p>
<p>The feeling of overspending when eating out fades in comparison to the satisfaction of investing in quality ingredients and creating something delicious oneself. The culinary process becomes enjoyable, a rewarding experience from start to finish.</p>
<p>Choosing home-cooked gatherings over restaurant invitations has become the norm. Suggesting a collaborative cooking session with friends strengthens bonds. When dining out is necessary, the focus shifts to finding affordable options that offer genuine value.</p>
<p>Exploring avenues to share this passion for cooking has opened new doors. Offering culinary services to friends and family for payment, and even considering catering opportunities, brings joy in creating experiences that bring people together through food.</p>
<p>Cooking transcends mere sustenance; it’s a powerful tool for connection and a way to positively impact lives. I’m very grateful for the connection cooking brings.</p>
<p>I’d really like to create more food, kitchen,  and cooking-related content. What kind of content resonates with you?</p>


';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>