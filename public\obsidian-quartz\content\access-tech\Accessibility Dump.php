<?php
// Auto-generated blog post
// Source: content\access-tech\Accessibility Dump.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'How are Blind people considered in Digital Design Principles?';
$meta_description = 'How are Blind people considered in Digital Design Principles? I had a difficult time reading a spreadsheet sent to me. The sheet was clunky, confusing...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'How are Blind people considered in Digital Design Principles?',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'How are Blind people considered in Digital Design Principles? I had a difficult time reading a spreadsheet sent to me. The sheet was clunky, confusing...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\Accessibility Dump.md',
);

// Post content
$post_content = '
<h2>How are Blind people considered in Digital Design Principles?</h2>
<p>I had a difficult time reading a spreadsheet sent to me. The sheet was clunky, confusing, straining to look at, and difficult to read. Things should be designed for simple and intuitive use. What does this look like with vision impairments?</p>
<p><em>Clunky AI depiction of a spreadsheet document.</em></p>
<h3>Principals of Universal Design</h3>
<em>Principle number three of universal design, simple and intuitive use</em>. <em>Principal six: __Low__ __Physical__ __Effort__.</em>  Modern world is not generally accessible for folks. I\'d like to work towards accessibility in design, and want to learn more about what I can learn and do to make a difference in this.
<h3>Barriers, Discrimination, and Lack of Access is killing us</h3>
Education. Job. Housing. Public Accomodation. I think about how inaccessible the world can be for disabled folks, whether that be folks with mental illness, neuro-divergencies, or a physical disability. The forty hour work week is not accessible for many people, especially if they are also trying to pursue higher education. I think about how our world lacks putting effort into accessibility for people, and instead puts extreme focus on profit and gate keeping. Higher Education is an example of this gate keeping, as it is not accessible for many. Even with grants, it can often be unmaintainable to pursue education while needing to continue working full time. This can be discouraging, and continue the cycle of poverty.
<h3>Americans with Disabilities Act of 1990</h3>
In 1990, the Americans with Disabilities Act was signed into federal law. The ADA has five Titles, most of which you referenced naturally. The ADA was written in a way where there is room for the meaning of the provisions to evolve with the progression of technology. Under Title IV, Telecommunications, as well as Title Three, Public Accommodations, there are solid teeth in the legislation to mandate standards of accessibility, in Digital Media Design. Within Telecommunications, which I am not read up on, there are standards around telephones, televisions, advertisements, and Creative Media. Under Public Accommodations (Title III), there are enforceable standards around websites, mainly governed by what is called the WCAG, or Web Content Accessibility Guidelines.
<h3>Department of Justice is going after inaccessible website design </h3>
On March of 2022, the Department of Justice issued a Media Statement that they are prioritizing resources towards enforcing website accessibility standards, and prosecuting civil suits brought on by complaints about inaccessible Public Accommodations online. If you are a Business or something that could be considered a Public Accommodation, and your content is not compliant/accessible, you could be held liable to pay thousands in fines, on top of have to hire an Accessibility Specialist to help remediate. There are AI widgets which claim to do this, some for a monthly premium, to display a widget on your website with added features to mitigate bad design, but many Disability Advocates have said these are terribly unreliable and unhelpful, and at best provide a very short term solution to what can be compared to having a poorly built building.
<h2>Personal Experience</h2>
Over the past year I have had the privilege to work online with a mentee in another state who has multiple conditions of blindness, and retains about 30% of his vision. Over all the things we worked on, one of them was me mapping out how he interacts with his Iphone and other Computer Technology. I\'m an Android User, and I was curious about how to help adjust settings, like not playing alarms when there were local alerts and stuff (<abbr title="Complex Post Traumatic Stress Disorder">CPTSD</abbr> from violence, loud noises, and sudden motions or events), making app font readable, etc. My Bachelors is in Psychology. And I am back in school studying Web Development and Design to remediate bad coding practices in websites. I am looking for support in getting my <abbr title="Certified Professional in Accessibility Core Competencies ">CPACC</abbr>.
<p><a href="https://www.youtube.com/watch?v=w2JBhke-H3w" class="external-link">(1) IAAP Accessibility Certification: Preparing for the CPACC, Looking Over the Content Outline - YouTube</a></p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/w2JBhke-H3w" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>
![[Pasted image 20230214205128.png]]</p>
<h3>Tools and Resources</h3>
Here are some free tools and resources to check out and play around with.
<p>#### WCAG Contrast Checker
This is a Browser plug in you can install for free very easily and do Color Contrast testing on pages. I use it when writing and consuming web pages all the time. Color Contrast is often an issue brought up in inaccessible design</p>
<p>I\'m going to info dump cause I like writing, the safeword is CatDog. If someone is Blind, they are more likely to know they are Blind and seek out accessibility software for their particular type of impairment. One of the most common software are screen readers. All major operating systems and internet browsers have native screen readers. Already installed on, no need for additional software.</p>
<p>The problem is, you need some common standard formatting standards for the experience to be pleasant, and for the content to be readable. You got to structure your documents with Heading Conventions, and do them right. It\'s a super easy practice to learn, and it\'s a good habit for anyone who writes anything.</p>
<p>This is another plug in for browsers called HeadingsMap. You can use it on any web page to view the structure. This is also helpful for navigation.</p>
<p>Doing these right in Word processors like Microsoft Word also helps navigation, and you can automatically generate a table of contents. It looks dope on anything over ten pages with good sectioning</p>
<p>Also Keyboard Navigation. For different operating systems, software, and internet browsers, there are ways to get around without a mouse. But it can be a terrible experience especially with websites when they aren\'t structured right.</p>
<p>One way to play around with it is to use Tab to navigate forward, Shift Tab to navigate backwards. Enter to select. Hotkey for screen reader would be up to your software and personal settings. use the arrow keys to move up and down a page slowly, and spacebar to scroll down very fast, I think shift + spacebar to scroll up very fast. Here is a site you can tinker on if you want to try <a href="https://l.facebook.com/l.php?u=https%3A%2F%2Fwww.petsradar.com%2Fadvice%2Fcan-cats-eat-dog-food%3Ffbclid%3DIwAR1Gqnlpoe__Hcf_b9jUimisqoDJPkKsUsKqKRv7br7YgKyD2dP2zkStewM&h=AT1peMt_HArwnkv1IazLIG-gtGmNqCBJwCfytJEUbcTV4HVPw5aVFET5WBhzp5RWkuqRMBdcH2ASQ9CcmkHKiXZA4DeQA6Pc6J2G1Rel5JjM9uuc9z5Ilx_yBCWQ2bRGOU91vA" class="external-link">https://www.petsradar.com/advice/can-cats-eat-dog-food</a></p>
<p>If you load the page and immediately before clicking anything hit tab, what will come up is \'Skip to Main Content.\' This skips to the main section in the HTML, skipping all the header links at the top, and all that jazz. Things that might be helpful, but not every time you load a page from the website.</p>
<p>I have seen a second one called \'Skip to Search\' similar to that but you can just search for anything and everything that might be on the site.</p>
<p>Ther is supposed to be a clearly delineated box when the keyboard mover goes over each element. You can see it happen with some stuff, but not most of it. All the browser things that get selected it makes a box around.</p>
<p>The site doesn\'t have the best ARIA, or Accessibility Rich Internet Applications. It\'s a way to do your website code that makes it easy for your browser to understand, this is what everything is. This is what everything does. This is how to communicate that best to the user.</p>
<p>This is the interview that got me really interested in the subject last year. In the interview Ashlee talks about having a jacket patch that says "No Aria is better than Bad Aria". Also between you and me they are both very nice on the eyes if you catch my drift (I think they are both fine af..). <a href="https://www.youtube.com/watch?v=qr0ujkLLgmE&t=6s&fbclid=IwAR15v2f9nkqgKdR1TXHKrVCi2SHD1sZ9Xe1jT0TTaiYdrTXGspxH_6jxxwk" class="external-link">https://www.youtube.com/watch?v=qr0ujkLLgmE&t=6s</a></p>
<p><a href="https://www.youtube.com/watch?v=qr0ujkLLgmE&t=6s&fbclid=IwAR1GvqZ4O0oXoejQ6-9NsHKFAEzxP3aw_cse1M3lVU0PgxA_7EdA_xVouA0" class="external-link">Getting started with web accessibility with Ashlee Boyer</a></p>
<p><a href="https://docs.google.com/document/d/1V4xK5dmh213eN6XH3ELVWcGVYiXpUzVP04KESvQQJeo/edit?usp=sharing" class="external-link">Goodwill Accessibility Audit by April</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>