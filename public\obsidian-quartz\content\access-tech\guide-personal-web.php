<?php
// Auto-generated blog post
// Source: content\access-tech\guide-personal-web.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Basic Guide to the Personal Web';
$meta_description = 'Basic Guide to the Personal Web - A. A. Chips';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Basic Guide to the Personal Web',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Basic Guide to the Personal Web - A. A. Chips',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\guide-personal-web.md',
);

// Post content
$post_content = '<h1>The Personal Web: A Better Alternative to Social Media?</h1>
<p>The Personal Web is an alternative to traditional social media that gives you more control over your online presence and data. Instead of relying on algorithms and corporate narratives, the Personal Web allows you to build your own website and connect with others through RSS feeds and online communities.</p>
<p>In this post, we\'ll explore the benefits of the Personal Web and how you can get started with building your own personal website.</p>
<h2>The Problem with Social Media</h2>
<p>Social media platforms like Facebook, Twitter, and Instagram have become an integral part of our online lives. However, they also have some major drawbacks. For one, they use algorithms to control what you see and when you see it, which can lead to echo chambers and the spread of misinformation. Additionally, they collect and sell your data to advertisers, which can be a violation of your privacy.</p>
<h2>The Benefits of the Personal Web</h2>
<p>The Personal Web is a decentralized network of personal websites that are connected through RSS feeds and online communities. This allows you to have more control over your online presence and data, as well as connect with others who share similar interests.</p>
<p>Some of the benefits of the Personal Web include:</p>
<p>* More control over your online presence and data
* Ability to connect with others who share similar interests
* Decentralized network that is not controlled by any one corporation
* Ability to build your own website and express yourself in a unique way</p>
<h2>How to Get Started with the Personal Web</h2>
<p>Getting started with the Personal Web is easier than you might think. Here are the basic steps:</p>
<p>1. Choose a domain name and web hosting service
2. Build your website using HTML, CSS, and JavaScript
3. Set up an RSS feed for your website
4. Join online communities and connect with others who share similar interests</p>
<p>There are also many resources available to help you get started, including online tutorials and communities of people who are already using the Personal Web.</p>
<h2>Conclusion</h2>
<p>The Personal Web is a great alternative to traditional social media that gives you more control over your online presence and data. By building your own website and connecting with others through RSS feeds and online communities, you can have a more authentic and fulfilling online experience. So why not give it a try?</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>