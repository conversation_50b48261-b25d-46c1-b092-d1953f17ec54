<?php
// Auto-generated blog post
// Source: content\kitchen\Bringing Eco-Stewardship into the Kitchen.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'april #writings #revisit #applechip #climate';
$meta_description = 'april writings revisit applechip climate --- Author:: April Cyr Key:: Private --- Integrating environmental responsibility into the kitchen. What does...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'april #writings #revisit #applechip #climate',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'april writings revisit applechip climate --- Author:: April Cyr Key:: Private --- Integrating environmental responsibility into the kitchen. What does...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\Bringing Eco-Stewardship into the Kitchen.md',
);

// Post content
$post_content = '<p>#april #writings #revisit #applechip #climate</p>
<p>---
Author:: April Cyr
Key:: Private</p>
<p>---</p>
<p>Integrating environmental responsibility into the kitchen. What does this mean for you?</p>
<p>Where I live, grocery stores present a global array of food, transported from distant lands. This system prioritizes consumer appeal and convenience for profit. Having always had ample access to food, I was curious about the origins and journeys of these items, and the conditions required to make them readily available to those who could afford them. Even at a young age, it seemed clear that this system was inherently unequal, both environmentally and for the majority of the global population.</p>
<p>Every food item has a unique story and origin. I want to dedicate my life to understanding the provenance of ingredients and their global impact. Without this intricate, exploitative global market, we would rely on our immediate environment and natural cycles. Why are fresh spring vegetables available year-round? What happens to meat stored for weeks? What is the role of farmers and food producers today? Lacking a related upbringing, my adult life has been dedicated to finding these answers, guided by invaluable teachers and experiences. I\'ve journeyed across 13 states, speaking with land stewards, visiting Native reservations, ecological sacrifice zones, and food deserts. Much of what I\'ve learned comes from others – individuals with deep ties to the land, often marked by histories of injustice. Some of these stories are difficult and personal, revealing the hidden costs of our food system, a reality I was largely shielded from. Regardless of your connection to food and the land, I believe that truth, even when uncomfortable, is liberating.</p>
<p>My aim is to incorporate dietary wisdom from my Sephardic Jewish heritage into an ethical framework that can reshape our habits towards environmental restoration.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>