<?php
// Auto-generated blog post
// Source: content\street\hobo-code-ethics.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Hobo Code of Ethics';
$meta_description = 'At the 1889 National Hobo Convention in St. Louis, a strict ethical code was established for all hobos to follow. Here are some tips we could all use, no matter what you carry in your rucksack.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Hobo Code of Ethics',
  'author' => 'Unknown',
  'date' => '2025-10-09',
  'excerpt' => 'At the 1889 National Hobo Convention in St. Louis, a strict ethical code was established for all hobos to follow. Here are some tips we could all use, no matter what you carry in your rucksack.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\street\\hobo-code-ethics.md',
);

// Post content
$post_content = '<p>At the 1889 National Hobo Convention in St. Louis, a strict ethical code was established for all hobos to follow. Here are some tips we could all use, no matter what you carry in your rucksack.</p>
<p>1. YOU DO YOU.</p>
<p>"Decide your own life, don\'t let another person run or rule you."</p>
<p>2. SHOW SOME RESPECT.</p>
<p>"When in town, always respect the local law and officials, and try to be a gentleman at all times."</p>
<p>3. DON\'T BE AN OPPORTUNIST.</p>
<p>"Don\'t take advantage of someone who is in a vulnerable situation, locals or other hobos."</p>
<p>4. GET A JOB.</p>
<p>"Always try to find work, even if temporary, and always seek out jobs nobody wants. By doing so you not only help a business along, but ensure employment should you return to that town again."</p>
<p>5. BE A SELF-STARTER.</p>
<p>"When no employment is available, make your own work by using your added talents at crafts."</p>
<p>6. SET A GOOD EXAMPLE.</p>
<p>"Do not allow yourself to become a stupid drunk and set a bad example for locals\' treatment of other hobos."</p>
<p>7. BE MINDFUL OF OTHERS.</p>
<p>"When jungling in town, respect handouts, do not wear them out, another hobo will be coming along who will need them as badly, if not worse than you."</p>
<p>8. DON\'T LITTER.</p>
<p>"Always respect nature, do not leave garbage where you are jungling."</p>
<p>9. LEND A HAND.</p>
<p>"If in a community jungle, always pitch in and help."</p>
<p>10. PRACTICE GOOD HYGIENE.</p>
<p>"Try to stay clean, and boil up wherever possible."</p>
<p>11. BE COURTEOUS WHEN YOU\'RE RIDING THE RAILS ...</p>
<p>"When traveling, ride your train respectfully, take no personal chances, cause no problems with the operating crew or host railroad, act like an extra crew member."</p>
<p>12. ... AND WHEN YOU\'RE NOT.</p>
<p>"Do not cause problems in a train yard, another hobo will be coming along who will need passage through that yard."</p>
<p>13. HELP OUT THE KIDS.</p>
<p>"Help all runaway children, and try to induce them to return home."</p>
<p>14. SAME GOES FOR HOBOS.</p>
<p>"Help your fellow hobos whenever and wherever needed, you may need their help someday."</p>
<p>15. LEND YOUR VOICE.</p>
<p>"If present at a hobo court and you have testimony, give it. Whether for or against the accused, your voice counts!"</p>
<p><em>Wait.. National Hobo Convention? Is that a real thing? Yes.</em></p>
<p>"The National Hobo Convention is held on the second weekend of every August since 1900 in the town of Britt, Iowa, organized by the local Chamber of Commerce, and known throughout the town as the annual "Hobo Day" celebration."</p>
<p><a href="https://www.atlasobscura.com/places/national-hobo-convention">https://www.atlasobscura.com/places/national-hobo-convention</a></p>
<p><a href="https://www.mentalfloss.com/article/68447/15-rules-hobo-ethical-code-1889">https://www.mentalfloss.com/article/68447/15-rules-hobo-ethical-code-1889</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>