<?php
// Auto-generated blog post
// Source: content\inspiration\why-didnt-she-leave.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Why Didn\'t She Just Leave?';
$meta_description = 'Why we should never say "But why don\'t you just leave?" to women in toxic, highly problematic marriages.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Why Didn\'t She Just Leave?',
  'author' => 'Anonymous',
  'date' => '2025-10-09',
  'excerpt' => 'Why we should never say "But why don\'t you just leave?" to women in toxic, highly problematic marriages.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\why-didnt-she-leave.md',
);

// Post content
$post_content = '<h1>Why Didn\'t She Just Leave?</h1>
<p>I found this reposted on a social media site and liked it.</p>
<p>Related: <a href="../street/when-choice-isnt-choice.php">If you choose to be homeless, it means your choices in the situation were terrible.</a></p>
<p>Anonymous:</p>
<p>“Why we should never say "But why don\'t you just leave?" to women in toxic, highly problematic marriages.</p>
<p>🌟 She was taught to fight for her marriage.
🌟 She can\'t support herself or her children.
!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png" class="external-link">🌟</a> She sees herself as a contributor to the problems; she\'s hoping he will improve as she improves.
🌟 She\'s doesn\'t have anywhere to go.
🌟 She\'s too overwhelmed to think clearly about her next step.
🌟 She believes God hates divorce.
🌟 Her spouse has promised to complicate her life if she leaves.
🌟 She doesn\'t want to fail.
🌟 She\'s heard, "marriage is hard, divorce is hard, choose your hard." She\'s chosen her hard.</p>
<p>🌟 Her church doesn\'t allow divorce other than for infidelity (and even then, they would first have to try and reconcile.)
🌟 Her spiritual sounding board, her church, is silent on abuse and consequences of unrepentant harmful sin in marriage. She\'s in the dark.
🌟 Other people have gone through worse, and they stayed put.
🌟 She wants her children to have both parents.
🌟 She\'s heard testimonies where people stayed and prayed, and their spouses changed after a long time.
🌟 She\'s dependent on her husband for documentation as an immigrant.
🌟 He\'s never hit her.
🌟 Sometimes she thinks the marriage problems are not that bad.
🌟 He\'s a man of God, and she doesn\'t want to ruin his ministry.
🌟 She loves him.</p>
<p>👉🏾 You see, there are bazillion reasons why a wife won\'t "just leave." It\'s more complicated than we think.”</p>
<p><img src="../../img/art/swancpr.jpg" width="250" alt="banksy art girl cpr on dead swan."></p>


';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>