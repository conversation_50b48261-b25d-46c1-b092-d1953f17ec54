<?php
// Auto-generated blog post
// Source: content\access-tech\ada-explained.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'accessibility #lawyer #complaint #disability #hhs #hse #index #lecture #manuals #notes #pss #professional #puzzle #school #vocation #webd';
$meta_description = 'accessibility lawyer complaint disability hhs hse index lecture manuals notes pss professional puzzle school vocation webd  --- Author:: Daniellabilit...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'accessibility #lawyer #complaint #disability #hhs #hse #index #lecture #manuals #notes #pss #professional #puzzle #school #vocation #webd',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'accessibility lawyer complaint disability hhs hse index lecture manuals notes pss professional puzzle school vocation webd  --- Author:: Daniellabilit...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\ada-explained.md',
);

// Post content
$post_content = '<p>#accessibility #lawyer #complaint #disability #hhs #hse #index #lecture #manuals #notes #pss #professional #puzzle #school #vocation #webd</p>
<p>---
Author:: Daniellability
Key:: Public</p>
<p>---</p>
<p>Transcript notes from <a href="https://www.youtube.com/watch?v=g1Q1BgpJnWI" class="external-link">The ADA Explained - YouTube</a> by Daniellability</p>
<h1>[[BlogPosts/sitehost/Articles _ Resources/Americans with Disabilities Act Explained]]</h1>
<iframe width="560" height="315" src="https://www.youtube.com/embed/g1Q1BgpJnWI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
<h2>History</h2>
+ Activist groups fighting for disability rights dating back to 1800s.
+ During Great Depression, League of the Physically Handicapped fought for employment for people with disabilities
+ In 1950, NARC or National Association for Retarded Children was founded. Not great name by todays\' standards.
+ In 1973, the Rehabilitation Act (section 504) was the first time the exclusion and segregation of people with disabilities was seen as discrimination. It was first time people with disabilities were viewed as a minority group. Section 504 of the Act banned discriminationa gainst people with disabilities in programs that get federal financial assistance. Before this, people had the mindset that the problems people with disabilities face (like unemployment or lack of education) were inevitable circumstances imposed by the disability itself. The Act started to change that mindset and help people realize it\'s often a result of societal barriers and prejudices. The first step to changing this was to define what non-discrimination means in the context of disability. 
+ In 1975, the <strong>Education of All Handicapped Children Act</strong> guaranteed children with disabilities the right to public school education. 
+ The first version of the ADA was introduced in April 1988. A national campaign was started to write Discrimination Diaries to document daily instances of inaccessibility and discrimination. This was used to help raise awareness about the barriers to daily living for people with disabilities. A couple examples of the discrimination that was found were restaurants refusing to service people with service dogs, wheelchair users having to abandon their wheelchairs if they wanted to ride a bus or train. Businesses could legally pay employees with disabilities less than non-disabled employees doing the same work, and refuse to hire people because they had a disability.
<h2>Passing of ADA</h2>
The Americans with Disabilities Act was put into effect by Congress on July 26, 1990. It bans discrimination based on disability, and requires public accommodations to be accessible. The Act defines disability as: a physical or mental impairment that substantially limits one or more major life activities. DIsabilities that are covered are any condition that substantially limit a major life activity, such as walking, talking, hearing, seeing or learning. People with a history of disability, such as cancer in remission, or an impairment that is not temporary, expected to alst over six months.
<h2>Title I: Employment</h2>
People with disabilities should have the same employment opportunities and benefits as everyone else. Enforcement is by the US Equal Employment Opportunity Commission (EEOC). This says there can\'t be discrimination in any aspect of employment, from Hiring, to Firing, to Pa, Job Assignments, Promotions, Layoffs, or Training.  The EEOC says that discrimination is when an employer treats a qualified individual unfavorably because they have a disability, and that employers provide reasonable accommodations which are basically modifications to a job or work environment that will enable an employee with a disability to do their job well, unless providing these accomodations will cause undo hardship or any kind of difficulty because of the employer\'s size, finances, or the needs of their business. All of this applies to businesses that have 15 or more employees.
<h2>Title II: Public Entities and Public Transportation</h2>
Requiring need for space and accommodation for wheelchairs on public transportation, and that people with disabilities need to be able toa ccess all state and local public housing. This title is enforced by the Office of Fair Housing and Equal Opportunity. It also prohibits discrimination in any kind of housing related transactions like sale, rental and financing. Another part of TItle II is the Architectural Barriers Act of 1968. This says that buildings that are designed, constructed, or altered in any way after September of 1969, need to be accessible for people with disab ilities.  Any building before that law usually aren\'t impacted, but a new business going into an older building can sometimes trigger changes. The Architectural Barriers Act is enforced by the Architectural and Transportation Barriers Compliance Board by investigating complaints. CHanges won\'t be made unless a complaint is made.
<h2>Title III: Public Accommodations and Commercial Facilities</h2>
People with disabilities will not be discriminated against in the ability to have full and equal enjoyment of the goods, services, facilities and accommodations of any public space. This applies to hotels, recreation, transportation, education, dining, stores, care providers, and public spaces. Exceptions include private clubs, unions, and religious organizations that are not bound by Title III. Discrimination here would be failure to remove architectural barriers in existing facilities. Especially when those barriers are easy to remove without much difficulty or expense. They do what\'s called a Balance Test where they  look at difference between cost of proposed fix, and financial means of the business. A change that may be really easy for a big corporation may be too small for a local business. With historic buildings, they have to do as many fixes as are possible without destroyig historic significance of building. Any new construction after ADA has to be fully compliant.
<h2>Title IV: Telecommunications</h2>
Telephone, Internet, and Radio. Requires that all telecommunications companies in the US take steps to ensure functionally equivalent services for consumers with disabilities, specifically people who are deaf, hard of hearign, or have speech impairments. An example of this could be having braille on keyboards or having options to have someone interpret for you over the phone.
<h2>Title V: Provisions</h2>
Ensures people who exercise their rights under the ADA or assit others in exercising their rights are protected from retaliation from anyone else.

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>