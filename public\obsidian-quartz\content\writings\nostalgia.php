<?php
// Auto-generated blog post
// Source: content\writings\nostalgia.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Nostalgia: A Poem';
$meta_description = 'I wrote this poem in 2011 while attending school near a former mental hospital. I was talking a Psychology course and we talked about the ways Nostalgia has been defined over the past few hundred years. This is a poem about that.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Nostalgia: A Poem',
  'author' => 'A. A. Chips',
  'date' => '2011-11-27',
  'excerpt' => 'I wrote this poem in 2011 while attending school near a former mental hospital. I was talking a Psychology course and we talked about the ways Nostalgia has been defined over the past few hundred years. This is a poem about that.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\writings\\nostalgia.md',
);

// Post content
$post_content = '<p><em>I wrote this poem in 2011 while attending school near a former asylum. I was talking a Psychology course and we talked about the ways Nostalgia has been defined over the past few hundred years. This is a poem about that.</em></p>
<p>A cerebro-neurological disorder caused by the quite continuous vibration of animal spirits through those fibers of the middle brain in which impressions, ideas, and traces, of the Fatherland still cling.</p>
<p>1688.</p>
<p>Sharp “atmospheric pressure differences, blood migrates from the heart to the brain, afflicting the observed with sentiment.</p>
<p>1732.</p>
<p>Indigenous to the Alpines, unremitting clanging of cowbells destroys the eardrum and brain cells.</p>
<p>Too much cowbell.</p>
<p>Anxiety.</p>
<p>Sadness.</p>
<p>Weakness.</p>
<p>Loss of Appetite.</p>
<p>Insomnia.</p>
<p>Fear.</p>
<p>Obsessive Homesickness.</p>
<p>Intense unhappiness.</p>
<p>Immigrant psychosis for the soldiers, seamen, immigrants, and first year college students.</p>
<p>I the protagonist.</p>
<p>Featuring them.</p>
<p>A way to cope with the present.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>