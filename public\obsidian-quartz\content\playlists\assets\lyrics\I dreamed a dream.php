<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\I dreamed a dream.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'D  D/C#  Bm  Bm/A  G  A';
$meta_description = 'D  D/C  Bm  Bm/A  G  A Verse D              D/C              Bm   Bm/A I dreamed a dream in time gone by G                ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'D  D/C#  Bm  Bm/A  G  A',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'D  D/C  Bm  Bm/A  G  A Verse D              D/C              Bm   Bm/A I dreamed a dream in time gone by G                ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\I dreamed a dream.md',
);

// Post content
$post_content = '<p>**</p>
<p>D  D/C#  Bm  Bm/A  G  A</p>
<p>[Verse]</p>
<p>D              D/C#              Bm   Bm/A</p>
<p>I dreamed a dream in time gone by</p>
<p>G                G/F#</p>
<p>When hope was high</p>
<p>              Em7     A</p>
<p>And life worth living</p>
<p>D                 D/C#            Bm7      Bm/A</p>
<p>I dreamed that love would never die</p>
<p>G                 G/F#             Em7           A</p>
<p>I dreamed that God would be forgiving</p>
<p>D              D/C#            Bm           Bm/A</p>
<p>Then I was young and unafraid</p>
<p>G                  G/F#                  Em7      A</p>
<p>And dreams were made and used and wasted</p>
<p>D               D/C#         Bm7              Bm/A</p>
<p>There was no ransom to be paid</p>
<p>G             G/F#           Em7              A</p>
<p>No song unsung, no wine untasted</p>
<p>[Chorus]</p>
<p>B                         Em</p>
<p>But the tigers come at night</p>
<p>B              B7             E</p>
<p>With their voices soft as thunder</p>
<p>A                          Dm</p>
<p>As they tear your hope apart</p>
<p>A                               D   Em   F#m   G   A</p>
<p>And they turn your dream to shame</p>
<p>[Verse]</p>
<p>D              D/C#        Bm    Bm/A</p>
<p>He slept a summer by my side</p>
<p>G                G/F#             Em7       A</p>
<p>He filled my days with endless wonder</p>
<p>D              D/C#            Bm7        Bm/A</p>

<p>He took my childhood in his stride</p>
<p>G             Asus       A      D    A   Am   B</p>
<p>But he was gone when autumn came</p>
<p>E              E/D#                C#m7        C#m/B</p>
<p>And still I dream he\'ll come to me</p>
<p>A                A/G#            F#m7         B</p>
<p>That we will live the years together</p>
<p>E                E/D#               C#m7       C#m/B</p>
<p>But there are dreams that cannot be</p>
<p>A                 A/G#            F#m7         B   E  E/D#</p>
<p>And there are storms we cannot weather</p>
<p>[Bridge]</p>
<p>C#m7        C#m/B              A    A/G#</p>
<p>I had a dream my life would be</p>
<p>F#m7            B                  E</p>
<p>So different from this hell I\'m living</p>
<p>            E/D#             C#m7            C#m/B</p>
<p>So different now from what it seemed</p>
<p>A               B                  E</p>
<p>Now life has killed the dream I dreamed.</p>
<p>    C/E     Fm</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>