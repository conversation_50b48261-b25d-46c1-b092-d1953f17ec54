<?php
// Auto-generated blog post
// Source: content\alienation\supported-decision-making.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'A Friendly Introduction into Supported Decision Making';
$meta_description = 'The traditional model of decision-making for individuals with disabilities often positions them as incapable and needing others to decide for them. This outdated approach, rooted in a guardianship or conservatorship model, has perpetuated harmful stereotypes and denied individuals their fundamental right to autonomy.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'A Friendly Introduction into Supported Decision Making',
  'author' => 'A. A. Chips',
  'date' => '2022-06-12',
  'excerpt' => 'The traditional model of decision-making for individuals with disabilities often positions them as incapable and needing others to decide for them. This outdated approach, rooted in a guardianship or conservatorship model, has perpetuated harmful stereotypes and denied individuals their fundamental right to autonomy.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\alienation\\supported-decision-making.md',
);

// Post content
$post_content = '<h3><strong>Supported Decision Making: A Fundamental Human Right</strong></h3>
<p>The traditional model of decision-making for individuals with disabilities often positions them as incapable and needing others to decide for them. This outdated approach, rooted in a guardianship or conservatorship model, has perpetuated harmful stereotypes and denied individuals their fundamental right to autonomy.</p>
<p>Supported Decision Making (SDM) offers a transformative alternative. It recognizes that every person, regardless of disability, is the foremost expert on their own life. SDM empowers individuals to make their own choices, with the assistance of trusted supporters who offer guidance and help them understand the implications of their decisions.
<h3><strong>Why is Supported Decision Making Important?</strong></h3></p>
<p>- <strong>It’s a human right:</strong>  The right to make decisions about one’s life is inherent to all individuals. SDM upholds this right, promoting self-determination and dignity.</p>
<p>- <strong>It challenges stereotypes:</strong> SDM counters the harmful notion that individuals with disabilities are incapable of making choices. It recognizes their agency and capacity to contribute meaningfully to their lives.</p>
<p>- <strong>It fosters independence:</strong> By providing support rather than taking over, SDM encourages individuals to develop their decision-making skills and become more self-reliant.</p>
<h3><strong>How Does Supported Decision Making Work?</strong></h3>
<p>SDM involves a collaborative process where the individual identifies trusted supporters who can:</p>
<p>- <strong>Provide information:</strong> Supporters help the individual gather and understand relevant information about their choices.</p>
<p>- <strong>Explore options:</strong> Supporters assist in exploring different options and potential outcomes.</p>
<p>- <strong>Communicate preferences:</strong> Supporters help the individual express their preferences and decisions clearly.</p>
<p>- <strong>Access resources:</strong> Supporters connect the individual with resources and services that can support their choices.</p>
<h3><strong>The Role of Professionals</strong></h3>
<p>As professionals in the human services field, it’s our duty to champion Supported Decision Making. We must affirm our clients’ autonomy and self-determination, working to dismantle the outdated guardianship system that often strips individuals of their rights.
<h3><strong>Learn More</strong></h3></p>
<p>I highly recommend the lecture and four-part video series on Supported Decision Making by UC Davis Minds Institute. Jonathan Martini, a lawyer and compelling speaker, argues passionately for our fundamental right to choose and presents evidence supporting the shift from guardianship to SDM.
<h3><strong>Let’s embrace Supported Decision Making and empower individuals to live self-directed lives.</strong></h3></p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/aI3aJfs6-X4" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>