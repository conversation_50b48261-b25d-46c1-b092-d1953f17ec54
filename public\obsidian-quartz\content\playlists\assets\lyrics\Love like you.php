<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Love like you.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Love Like You';
$meta_description = 'Love Like You Rebecca Sugar & Steven Universe C            Dm Em               Fm       C             A If I could begin to be H...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Love Like You',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Love Like You Rebecca Sugar & Steven Universe C            Dm Em               Fm       C             A If I could begin to be H...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Love like you.md',
);

// Post content
$post_content = '<p>**</p>
<p>Love Like You</p>
<p>Rebecca Sugar & Steven Universe</p>
<p>C            Dm Em               Fm       C             A</p>
<p>If I could begin to be Half of what you think of me I could do about anything</p>
<p>            D            G C              Dm Em                 Fm</p>
<p>I could even learn how to love When I see the way you act Wondering when I\'m coming back</p>
<p>       C             A            D            G     Cmaj7</p>
<p>I could do about anything. I could even learn how to love. Like you</p>
<p>[Bridge]</p>
<p>        F           Fm       Em               A</p>
<p>I always thought I might be bad Now I\'m sure that it\'s true</p>
<p>       F               G       Em           A7</p>
<p>Cause I think you\'re so good And I\'m nothing like you</p>
<p>           F       Fm Em            A      </p>
<p>Look at you go I just adore you I wish that I knew </p>
<p>        F      G7</p>
<p>What makes you think I\'m so special</p>
<p>[V2]</p>
<p>C            Dm Em                  Fm       C             A</p>
<p>If I could begin to do Something that does right by you I would do about anything</p>
<p>            D             G C              Dm</p>
<p>I would even learn how to love When I see the way you look</p>
<p>Em            Fm       C             A</p>
<p>shaken by how long it took I could do about anything</p>
<p>            D            G    Cmaj7</p>
<p>I could even learn how to love. Like you</p>
<p>[Interlude]</p>
<p>Cmaj7 Fmaj7 Cmaj7</p>
<p>[Outro]</p>
<p>C             Dm Em                Fm</p>
<p>If I could\'ve stayed to be Everything you\'ve thought of me</p>
<p>        C               A                 D              G</p>
<p>Could\'ve done about anything But I think I\'ve learned how to love</p>
<p>    Cmaj7</p>
<p>Like you</p>
<p>**</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>