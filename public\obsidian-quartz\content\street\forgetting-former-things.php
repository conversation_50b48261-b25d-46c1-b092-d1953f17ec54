<?php
// Auto-generated blog post
// Source: content\street\forgetting-former-things.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Forgetting the Former Things';
$meta_description = 'I woke up this morning uneased after having back to back flashbacks over dream. It was a rough morning. I got a lot of solace reading the introduction to the book Forgetting the Former Things by Tamara Puffner and Joyce Hollyday. I\'ve not felt more seen by a passage in a book for a long time.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Forgetting the Former Things',
  'author' => 'A. A. Chips & Tamara Puffner',
  'date' => '2025-10-09',
  'excerpt' => 'I woke up this morning uneased after having back to back flashbacks over dream. It was a rough morning. I got a lot of solace reading the introduction to the book Forgetting the Former Things by Tamara Puffner and Joyce Hollyday. I\'ve not felt more seen by a passage in a book for a long time.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\street\\forgetting-former-things.md',
);

// Post content
$post_content = '<p>This morning was difficult due to recurring flashbacks. Reading the introduction to Forgetting the Former Things by Tamara Puffner and Joyce Hollyday provided solace and resonated deeply. The introduction discusses the fatal shooting of Keith Lamont Scott, a brain injury survivor, by a police officer in September 2016. Scott was waiting for his son and had just taken his medication, likely impacting his ability to respond quickly to the officer\'s demands.</p>
<p>A significant factor in leaving my hometown in January 2017 was experiencing [[verbal shutdown]] when communicating with my mother. She threatened to involve the police with an All Points Bulletin if I didn\'t respond to her texts, despite other non-law enforcement options being available. At the time, I was living out of my car. While the likelihood of a fatal encounter may have been lower due to my race, involving armed law enforcement in a welfare check on someone living in a vehicle presents inherent risks. For approximately three years, coinciding with the 45th presidential administration, I did not trust my mother having access to my location due to a heightened emotional state and a sense of general uncertainty. Attempts at healthy communication have been unsuccessful, leading to the decision to share these personal experiences publicly on this site. The news surrounding events like Keith Lamont Scott\'s death contributed to my decision to relocate to North Carolina. I am a [[no-fly-zone]].</p>
<p>From <em>Forgetting the Former Things</em> by Tamara Puffner:</p>
<p><blockquote>On the afternoon of September 20, 2016, I was at my home in Asheville, North Carolina, writing a section of this book. One hundred and thirty miles away in Charlotte, a police officer fatally shot Keith Lamont Scott while he was waiting in the parking lot at his apartment complex for his son to return home from school. The killing prompted three nights of protest, a declaration of a state of emergency by our governor, and a call to the National Guard to back up police, who employed tear gas and rubber bullets when the largely nonviolent protests grew confrontational. By the time it was over, several people were injured and another man wan dead.<br><br></p>
<p>Reports conflict describing Mr. Scott\'s killing, from his wife Rakeyia Scott, other eyewitnesses, and the police officers on the scene. Available video footage is inconclusive on the details in dispute. But what is clear from the record on Ms. Scott\'s cellphone is that she said to the police repeatedly, "Don\'t shoot him. He has no weapon."<br><br></p>
<p>Officers continued yelling, "Drop the gun!" Ms. Scott repeated her pleas. "He doesn\'t have a gun!" she shouted. "He has a TBI. He\'s not going to do anything to you guys. He just took his medicine." When Mr. Scott got out of his vehicle and began backing away from it, a police officer opened fire and killed him.<br><br></p>
<p>The tragedy raised strong emotions in me: anger, frustration, grief. I wondered, did those officers even know that a TBI is a traumatic brain injury? Did they have any understanding that a person with a TBI in such a stressful situation would feel disoriented and act in ways that might seem unusual?<br><br></p>
<p>Keith Lamont Scott had sustained a brain injury in a motorcycle accident, in which he also broke both his hips and his nose, in November 2015. Afterward, according to his family, he had difficulty with speech and memory. In the aftermath of his killing, Susan H. Connors, president and CEO of the Brain Injury Association of America, released a statement to the press. It said in part: "Traumatic brain injury can result in problems with receptive language, or understanding what is being said, and individuals may have a delayed reaction to commands. It is important that public officials and first responders throughout the country come to understand the complexities involved with brain injury."!<br><br></p>
<p>I have wished for greater understanding of brain injury ever since the August 1996 car accident that changed my life. I\'m heartened that brain injury and other cognitive disabilities have recently appeared in the national spotlight. But I\'m saddened that this increased attention has come largely as a result of all the TBIs suffered by veterans of the Iraq and Afghanistan wars injured by IEDs (improvised explosive devices); the revelation of widespread CTE (chronic traumatic encephalopathy) among football players; and the epidemic of Alzheimer\'s disease and other forms of dementia among our elders.<br><br></p>
<p>As a Presbyterian pastor, I\'ve paid attention to how often brain injury survivors and their families in the support groups I\'ve attended have spoken about feeling uncomfortable in church. Most have found that stating their needs is difficult, and their injuries are frequently misunderstood. The 1990 Americans with Disabilities Act launched a round of conversations and study that placed the issue of disability squarely into the life of the church. Many local churches began to address accessibility with heightened resolve.<br><br></p>
<p>But I was left wondering: What about the concerns that can\'t be solved with an elevator or a wheelchair ramp? What about the "invisible" reality of cognitive disability? What about the many people who leave our churches feeling hurt and misunderstood, who have been treated as charity cases or objects of pity, receiving offers of help but never invitations to offer their gifts?<br><br></p>
<p>Recent years have brought an increase in books about theology and disability, but few address brain injury and other cognitive challenges. Those that do are written almost exclusively by researchers, observers, or caregivers. I wrestled for a few years over whether to write this book. It was not easy. The process of writing highlighted my cognitive deficits, pushed the limits of my stamina, and drained my patience. I don\'t remember everything that happened to me around my accident. Focusing on what I can recall and recording the traumas of that moment, my long rehabilitation, and my ongoing losses raised a mountain of emotions.<br><br></p>
<p>But I\'m glad that I persisted. My connection with other brain injury survivors, and the responses I\'ve received to my blogs, articles, and sermons, have convinced me that the need is great for sharing experiences and engaging in public conversation about the growing reality of brain injury and its impact on the church and our culture more broadly. I hope that Forgetting the Former Things will contribute to that ongoing conversation.<br><br></p>
<p>There\'s a saying in the brain injury community: "If you know one person with a brain injury ... you know one person with brain injury." I decided to write this book about my experience in part because I can. So many others with brain injuries have lost that ability, landing at a different place on the broad spectrum of function. I don\'t claim to speak for them, but I hope that my personal and theological reflection in these pages can be a lens through which they and those around them can understand their struggles and triumphs, helping to move brain injury further out of the realm of invisibility.<br><br></p>
<p>This book is intended for brain injury survivors, their partners and families, and the pastors, teachers, and medical professionals who serve them. It is also for all the people who feel that their life hasn\'t turned out the way they had envisioned it: for all those who have suffered the rupture of illness or divorce, the loss of a job or the unexpected death of a loved one. I have given it the title Forgetting the Former Things. <br><br></p>
<p>When I was coming out of a two-week induced coma after my accident, two verses from my favorite Old Testament prophet, Isaiah, ran repeatedly through my mind: "Do not remember the former things, or consider the things of old. I am about to do a new thing." The book title is a double entendre that speaks of the forgetting and memory challenges that result from brain injury, and the need to let go of what once was in order to be open to  be imagining one\'s life.<br><br></p>
<p>I\'ve come a long way. And, in many ways, I\'m still waiting to see what \'new thing" is around the next corner. I cling to Isaiah\'s promise that God has claimed, redeemed, and called me by name. God is leading me through a wilderness, making a path that isn\'t always dear to me. But I have not been overwhelmed or consumed. <br><br></p>
<p>I have not been abandoned. And I need not fear.</p>
<p></blockquote></p>
<p><a href="https://www.logos.com/product/177613/forgetting-the-former-things-brain-injurys-invitation-to-vulnerability-and-faith" target="_blank">You can view more about Tamara\'s book, Forgetting the Former Things, and possibly buy it here.</a></p>
<p><img src="../../img/forgetting-the-former-things.webp" alt="Forgetting the former things book cover Tamara Puffner Joyce Hollyday." width="200"></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>