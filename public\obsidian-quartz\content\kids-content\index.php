<?php
// Category index for Kids Content
// Load path helper and configuration
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Kids Content';
$meta_description = 'Browse all posts in the Kids Content category';
$meta_keywords = 'kids-content, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Scan for posts in this category
$category_posts = [];
$categoryDir = __DIR__;
$mdFiles = glob($categoryDir . '/*.md');

foreach ($mdFiles as $mdFile) {
    $filename = basename($mdFile, '.md');
    
    // Skip index.md if it exists
    if ($filename === 'index') {
        continue;
    }
    
    // Check if corresponding PHP file exists
    $phpFile = $categoryDir . '/' . $filename . '.php';
    if (file_exists($phpFile)) {
        // Parse markdown file for metadata
        $content = file_get_contents($mdFile);
        $title = $filename;
        $excerpt = '';
        
        // Extract title from first line if it's a heading
        if (preg_match('/^#\s+(.+)$/m', $content, $matches)) {
            $title = trim($matches[1]);
        } else {
            // Convert filename to readable title
            $title = ucwords(str_replace(['-', '_'], ' ', $filename));
        }
        
        // Extract excerpt from content (first paragraph)
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && !preg_match('/^#/', $line) && !preg_match('/^---/', $line)) {
                $excerpt = substr($line, 0, 150);
                if (strlen($line) > 150) {
                    $excerpt .= '...';
                }
                break;
            }
        }
        
        $category_posts[] = [
            'title' => $title,
            'url' => $filename . '.php',
            'excerpt' => $excerpt ?: 'Educational and entertaining content for children.',
            'date' => date('Y-m-d', filemtime($mdFile)),
            'author' => 'A. A. Chips',
            'tags' => ['kids', 'education']
        ];
    }
}

// Sort posts by date (newest first)
usort($category_posts, function($a, $b) {
    return strcmp($b['date'], $a['date']);
});

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
        <p class="category-description">Educational and entertaining content for children</p>
    </header>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author']): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo htmlspecialchars($post['date']); ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>
