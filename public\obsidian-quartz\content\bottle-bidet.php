<?php
// Auto-generated blog post
// Source: content\bottle-bidet.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Improvised Bidet Washing';
$meta_description = 'Since the toilet paper scares in 2020, I have washed my behind with a squeeze bottle originally intended for barbecue sauce.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Improvised Bidet Washing',
  'author' => 'A. A. Chips',
  'date' => '2025-05-01',
  'excerpt' => 'Since the toilet paper scares in 2020, I have washed my behind with a squeeze bottle originally intended for barbecue sauce.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\bottle-bidet.md',
);

// Post content
$post_content = '<p>In 2020, I adopted water based cleaning to my behind. We were seeing people buy out ridiculous inventories of toilet paper prior to potential lockdowns. It\'s like all these people needed to survive was something to wipe their butt with.</p>
<p><img src="https://i.ytimg.com/vi/qJehYddjLJ4/maxresdefault.jpg" width="400" alt="There\'s got to be a better way."></p>
<p>With finances tight, getting a fancy bidet was not really an option. So instead, I bought a six pack of empty plastic sauce bottles for 1.09 a piece. Great purchase.</p>
<p>I posted about this back then on Reddit, on r/survival. I don\'t have any pictures. But I got several hundred upvotes. It was the most viral I have ever gone on Reddit.</p>
<p><img src="https://img2.tradewheel.com/uploads/images/products/9/2/peri-bottle-bidet-portable-recovery-upside-down-squeeze-bottle-for-postpartum-care-hemorrhoid-treatment-travel-portable-bidet1-0888008001672176648.jpg.webp" width="400" alt="Peri Bottle Bidet"></p>
<p>They make these, particularly for post-partem, but most squeeze bottles that can yield a strong narrow stream of water will do.</p>
<p>Five and a half years later, I have no regrets, and recommend others to try this great lifehack out. Save trees, save money, save time.</p>
<p>I actually ended up getting a small sponsorship / discount price on a bulk order for a product made out of Spain called Culo Clean. If you don\'t know what Culo is in Spanish, bless your heart.</p>
<p><img src="https://www.aventurenordique.com/media/catalog/product/cache/1/image/1800x/040ec09b1e35df139433887a97daa66f/b/i/bidet-portable-ultraleger-culoclean_bleu.jpg" alt="culo clean bidet bottle attachment" width="400"></p>
<p>I don\'t have a working webstore or dropship operation yet, but I have about fifty of these I love trading or giving as gifts. If you have the means, hit me up for one.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>