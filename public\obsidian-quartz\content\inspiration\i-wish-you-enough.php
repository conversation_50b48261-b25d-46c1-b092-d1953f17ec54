<?php
// Auto-generated blog post
// Source: content\inspiration\i-wish-you-enough.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '"I love you and I wish you enough."';
$meta_description = 'Recently, I overheard a mother and daughter in their last moments together at the airport as the daughter\'s departure had been announced. Standing nea...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '"I love you and I wish you enough."',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Recently, I overheard a mother and daughter in their last moments together at the airport as the daughter\'s departure had been announced. Standing nea...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\i-wish-you-enough.md',
);

// Post content
$post_content = '<p>Recently, I overheard a mother and daughter in their last moments together at the airport as the daughter\'s departure had been announced. Standing near the security gate, they hugged and the mother said:</p>
<p>"I love you and I wish you enough."</p>
<p>The daughter replied, "Mom, our life together has been more than enough. Your love is all I ever needed. I wish you enough, too, Mom." They kissed and the daughter left.</p>
<p>The mother walked over to the window where I sat. Standing there, I could see she wanted and needed to cry.</p>
<p>I tried not to intrude on her privacy but she welcomed me in by asking, "Did you ever say good-bye to someone knowing it would be forever?" "Yes, I have," I replied. "Forgive me for asking but why is this a forever good-bye?"</p>
<p>"I am old and she lives so far away. I have challenges ahead and the reality is the next trip back will be for my funeral," she said.</p>
<p>When you were saying good-bye, I heard you say, "I wish you enough." May I ask what that means?"</p>
<p>She began to smile. "That\'s a wish that has been handed down from other generations. My parents used to say it to everyone." She paused a moment and looked up as if trying to remember it in detail and she smiled even more.</p>
<p>"When we said \'I wish you enough\' we were wanting the other person to have a life filled with just enough good things to sustain them". Then turning toward me, she shared the following, reciting it from memory,</p>
<p>"I wish you enough sun to keep your attitude bright.
I wish you enough rain to appreciate the sun more.
I wish you enough happiness to keep your spirit alive.
I wish you enough pain so that the smallest joys in life appear much bigger.
I wish you enough gain to satisfy your wanting.
I wish you enough loss to appreciate all that you possess.
I wish you enough hellos to get you through the final good-bye."</p>
<p>She then began to cry and walked away.</p>
<p>They say it takes a minute to find a special person. An hour to appreciate them. A day to love them. And an entire life to forget them.</p>
<p>-Author Unknown</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>