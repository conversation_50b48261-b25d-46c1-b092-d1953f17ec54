<?php
// Auto-generated blog post
// Source: content\safeguarding\meltdowns-explained.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'autism #library #resources #vocation #hhs #hse';
$meta_description = 'autism library resources vocation hhs hse --- Author:: Amethyst Schaber Transcription:: April Cyr Key:: Public --- Ask an Autistic 15 - What are Autis...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'autism #library #resources #vocation #hhs #hse',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'autism library resources vocation hhs hse --- Author:: Amethyst Schaber Transcription:: April Cyr Key:: Public --- Ask an Autistic 15 - What are Autis...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\safeguarding\\meltdowns-explained.md',
);

// Post content
$post_content = '<p>#autism #library #resources #vocation #hhs #hse</p>
<p>---
Author:: Amethyst Schaber
Transcription:: April Cyr
Key:: Public</p>
<p>---</p>
<p>**Ask an Autistic #15 - <a href="https://www.youtube.com/watch?v=FhUDyarzqXE" class="external-link">What are Autistic Meltdowns?</a> - by Amythest Schaber</p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/FhUDyarzqXE" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe></p>
<p><strong>What are Autistic Meltdowns?</strong>
-   Meltdowns aren’t exclusive to autism. 
-   Physiological FIGHT OR FLIGHT response. Surges of adrenaline. Body is reacting as if LIFE OR DEATH situation. I am scared of you right now. 
-   Most common cause of meltdown is SENSORY OVERLOAD. Relevant to anyone with sensory integration issues.
-   Loud music, Protests, Riot Police use of  Long Range Acoustic Device Weaponry, being yelled at in close range by unstable and violent individuals.
-   Threshold of sensory stimulation able to endure. Changes day to day. 
-   When threshold exceeds. Extra stimulation may lead to overload. Mind = Maxed out.
-   <a href="https://vimeo.com/channels/staffpicks/52193530
-   At best of times, certain noises, like metal scraping on metal, (or voices" class="external-link">What Sensory Overload Is Like</a>, bright lights, scratchy clothing, can cause pain and discomfort. (FEELS LIKE RAZORBLADES ON EARS. FEELS LIKE RAZORBLADES ON EARS) 
-   During sensory overload, these are 10x worse
-  ==During this time, person will need to escape to safe place. Usually somewhere quiet and dark. When person cannot escape or have to endure more sensory stimulation, will have meltdown. ==</p>
<p>What does a Meltdown Feel like?</p>
<p>-   Every person is different and will experience differently. 
-   During a meltdown, so much is going on, it’s hard to know what you are feeling or even remember it later.
-   Lot of very strong and negative emotions
-   Adrenaline rush + Mental Breakdown + Panic Attack into one. ‘After I have a meltdown, it feels like I have been hit by a truck, and have the flu, and just ran a marathon.’ Exhausted, Icky, much more emotionally vulnerable after.
-   During meltdown person loses control. May scream, shout, drop to ground, throw things, kick, punch, cry inconsolably. Not be able to do anything but rock.
-   ==After meltdown, crucial time after where need to be alone. Need TLC. Have quiet time to recharge.==</p>
<p>Common Meltdown Cause: Prolonged Stress or Anxiety</p>
<p>-   Anxiety over prolonged period of time.
-   Increase in meltdowns - Increase in responsibilities and demands
-   Change in routine very difficult and can lead to meltdowns. (like sleep schedule changes and disrupted work routine)
-   Need downtime to recharge. Be by themselves. REST.
-   No unstructured downtime leads to more stress, meltdown, and sometimes burnout.</p>
<p>How do I help my autistic loved one through meltdowns?</p>
<p>-   Best method: PREVENT FROM HAPPENING IN FIRST PLACE.
-   If many meltdowns, indication of underlying problem needs to be addressed. Ex: Bullying, Over-scheduling, Abuse, no Accommodation in daily life
-   Meltdowns do not just happen. Not mysterious and unknowable. Always have a cause. Always something contributing and triggering.
-   Accommodate sensory needs.</p>
<p>See ‘What is Stimming?’</p>
<p>-   Best healthiest way to self-regulate. Block out negative sensory input. Or Deal with sensory stimulation.  
-   Way to express self.
-   May look like: giving noise canceling headphones, hat with brim for bright lights, secondhand clothes worn down to more comfortable texture. 
-   Simply not going where something will bother that person.</p>
<p>Conclusion</p>
<p>-   Difficult, not impossible to manage.
-   Prevent meltdowns by checking self
-   Accommodate by letting them do what they need to do.
-   As hard as autistic meltdowns are for loved ones, TEN TIMES WORSE FOR PERSON HAVING MELTDOWN. </p>
<p><a href="https://www.youtube.com/watch?v=alFO0HwC3gQ" class="external-link">What Shouldn\'t I Say to Autistic People?</a></p>
<p>‘I never would have guessed you were autistic.’ If the person knows how to pass it’s a thing of survival and not a positive thing. Not a compliment. Part of who the person is.</p>
<p>‘You must be very high-functioning.’ Labels inaccurate. False dichotomy. Dismissive to life experiences.</p>
<p>‘Isn’t that just like, socially RETARDED?’ Not word to be used. Like the N word. Slur to dehumanize. Complex neurological disorder with many facets. </p>
<p>‘Are you sure? Autism is really over-diagnosed nowadays.’ If somebody comes up to you as autistic, they know their life and experience much better than you do, they are trusting you to confide this. Do not downplay or make light or suggest otherwise. Do not suggest therapies. Do not try to evaluate or fix. Don’t try to change. Just accept the person.</p>
<p>A meltdown is NOT a tantrum. Please assist me to a safe, quiet place to de-escalate.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>