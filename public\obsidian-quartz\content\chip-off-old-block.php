<?php
// Auto-generated blog post
// Source: content\chip-off-old-block.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Chip Off the Old Block';
$meta_description = 'Recently I was invited to participate in a local art project called Unlabel Me. This project addresses the stigmas and labels that are used to dehumanize us, and break through them. I am planning to speak for their showcase event on June 13 for 2-4 minutes. I was given a prompt with several open ended reflection questions.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Chip Off the Old Block',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Recently I was invited to participate in a local art project called Unlabel Me. This project addresses the stigmas and labels that are used to dehumanize us, and break through them. I am planning to speak for their showcase event on June 13 for 2-4 minutes. I was given a prompt with several open ended reflection questions.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\chip-off-old-block.md',
);

// Post content
$post_content = '<p>Recently I was invited to participate in a local art project called Unlabel Me. This project addresses the stigmas and labels that are used to dehumanize us, and break through them. I am planning to speak for their showcase event on June 13 for 2-4 minutes. I was given a prompt with several open ended reflection questions. This is my response:</p>
<p>I am actively putting this website together to address and answer many of these questions. On this page, I will answer the prompt questions, but I will also hyperlink other pieces on here that I think are helpful and interesting to someone looking to get to know me better.</p>
<p><img src="../../img/self/chipoffblock.png" alt="Chips graphic for unlabel me. Chips 12 Baskets Café Patron. I was homeless for several years, living out of my car. Now I\'m building a project called Heartwarmers to connect free offerings and services to at-risk individuals. I survived sleeping in my vehicle during cold winters by using reusable water bottles filled with hot water to stay warm. I call tehse bottles Heartwarmers. label me problem child. unlabelme \'chip off the old block\'" width="400"></p>
<h3>What is your calling / work?</h3>
<p>In my life, there was a ten year period of instability (around 2010 to 2020) and change that I refer to as the \'rabbit hole\'. In this time I experienced extreme health issues, a full university education, homelessness, losing everything, bearing witness to extreme violence, greyhounding between 33 different states, and walking across 14 states. Since reentering housing I have gone back to school and learned how to code and a lot about how computers work. Many times when I experienced very difficult things, I would have something in mind of tools that don\'t exist, but could, which would have prevented those adversities. I have lots of data from that time in my life, which I meticulously journaled. I\'m planning to put more and more of that information on this site, as well as create many of those tools.</p>
<p>One of these tools, which I want to focus in on this year, is called <a href="https://www.aachips.co/heartwarmers" target="_blank">Heartwarmers</a> and it is meant to be an interactive platform for both mapping free and low cost resources to the at-risk public, facilitating local wish list connections (for both at risk persons and groups doing street distribution), and to let buskers and panhandlers set up a profile page that people can visit and donate electronically, as well as learn more about who they are and what their story is. This is meant to be a collectively volunteer driven not for profit online project and I am looking for minions to help this launch this upcoming cold season.</p>
<h3>What do you wish for?</h3>
<p>I wish for my story and brain to be publicly available to anyone who can benefit from it. I\'ve been leaning into some of the artificial intelligence tools coming out, not because I think they are some magic breakthrough that changes everything, but because they are here, and they help turn my work that would take decades, into something I can do in a year.</p>
<h3>How do you think / feel about the community at 12 Baskets and what would you like people to know about this community?</h3>
<p>There really isn\'t anything special about it. That isn\'t a sleight and I mean it in the most uplifting way possible. You can replicate every aspect of the Café elsewhere. Having a \'<a href="https://www.shankerinstitute.org/blog/what-are-third-places-and-why-do-they-matter" target="_blank">third-space</a>\' alone will draw people in who both need the material benefits it offers, and the social aspects as well. As long as there is food waste present you can find ways to redirect it and share it. It takes time, people, energy, and some resources. It will probably look different in different places. Don\'t get attached to the details.</p>
<h3>Metaphor for life in this place and time?</h3>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/qZR76i_pB_w?si=awr3sJcBM2ItqLcg" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<h3>What do you know about yourself/life/the world that others might not know?</h3>
<p>Schools, such as colleges and universities right now are struggling to maintain and grow enrollment since the pandemic. A lot of their funding comes from federal funding and grants that are dependent on maintaining and growing tuition numbers. This may change with the new administration and the sweeping cannibalistic cuts and power grabs to higher education. However, as a whole, schools are desperate for new student enrollment. If you ever wanted to go to, or go back to school, now is a great time. If you are a person with adverse experiences, or belong to a marginalized group, there may be opportunities present to pay for school completely.</p>
<h2>Linked Content</h2>
I\'m still working on this website. Many of the links are funky/broken. So I\'m sharing a list here of helpful content links to check out to understand me and my life better.
<p><ul>
  <li><a href="about-me.php" class="internal-link">Here are some blurbs about me and the work that I am doing.</a></li>
  <li><a href="https://www.aachips.co/heartwarmers" target="_blank">Here is the current website for Heartwarmers, the interactive platform I am looking for <strike>minions</strike> volunteers to help build.</a></li>
  <li><a href="alienation/dad-wasnt-who-they-said.php">This is a video by Ryan Thomas which explains his journey of discovering the truth about his father after years of being alienated and being told lies about who he was.</a></li>
  <li><a href="alienation/index.php">Here is an entire vault of content and resources regarding family and parental alienation. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li>
  <li><a href="contents.php">Here\'s a list of all the contents I have up right now on my site. It\'s not particularly organized, but if you are feeling nosy and want to explore, this is a good place to start.</a></li>
  <li><a href="100-things-about-myself.php">This is an exercise I am doing for our stand up comedy group that meets on Friday nights. I am sharing 100 things I know about myself. I am up to maybe 35.</a></li>
  <li><a href="come-to-me.php">This is an open invitation, rules, and guidelines for new connection in my life. While I enjoy my space, and am not seeking anything from others currently, I know there are people in the world who need what I have to offer. If someone seeks to get close to me in some way, I will likely share this to ensure everyone is on the same page.</a></li>
  <li><a href="founding-patron-12b.php">I am a founding patron at the 12 Baskets Café. Here is what that means to me.</a></li>
  <li><a href="humor/index.php">Here\'s a bunch of funny things I\'ve saved and bookmarked off the internet.</a></li>
  <li><a href="inspiration/index.php">Here\'s a bunch of inspirational things I\'ve saved and bookmarked off the internet.</a></li>
</ul></p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>