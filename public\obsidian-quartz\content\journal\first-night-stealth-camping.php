<?php
// Auto-generated blog post
// Source: content\journal\first-night-stealth-camping.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'First Night Stealth camping - 1/25/2017';
$meta_description = 'My initial days have been quite an experience. Arriving late on the first day, I spent the night stealth camping and enjoyed sleeping in my hammock beneath the stars.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'First Night Stealth camping - 1/25/2017',
  'author' => 'A. A. Chips',
  'date' => '2017-01-25',
  'excerpt' => 'My initial days have been quite an experience. Arriving late on the first day, I spent the night stealth camping and enjoyed sleeping in my hammock beneath the stars.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\first-night-stealth-camping.md',
);

// Post content
$post_content = '<p>My initial days have been quite an experience. Arriving late on the first day, I spent the night stealth camping and enjoyed sleeping in my hammock beneath the stars. Despite waking to drizzle at 4 AM and moving to sleep in the driver\'s seat, I had a satisfying night\'s rest.</p>
<p>The second day started positively with a morning hike organized through Meetup, where I connected with some wonderful local people, including Richard, Monica, Jan, and Julie, among others whose names I don\'t recall. I also met Gil at a coffee shop earlier. Later, while calling Ethan at Wendy\'s, I met Sam, another fellow traveler. We shared stories over tea and had a great conversation.</p>
<p>My plan for tomorrow involves finding a suitable parking spot to organize my car, which will be a significant task. Following that, I intend to print a message and visit various restaurants and food establishments strategically. Before doing so, I want to freshen up and change into clean clothes, utilizing baby wipes and a spare set I have. Later in the day, there\'s a Standing Rock event that I\'m interested in attending to connect with individuals who support Native Solidarity. I\'m really enjoying it here. Ethan is considering a fresh start and plans to join me in a few months. We\'re looking forward to renting a place together, which I think will be wonderful, and I especially can\'t wait to cook with him regularly.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>