<?php
// Auto-generated blog post
// Source: content\inspiration\man-in-glass.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Man in the Glass';
$meta_description = 'You can fool the whole world down the pathway of years, and get pats on the back as you pass, but your final reward will be heartaches and tears if you\'ve cheated the man in the glass';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Man in the Glass',
  'author' => 'Dale Wimbrow',
  'date' => '2025-10-09',
  'excerpt' => 'You can fool the whole world down the pathway of years, and get pats on the back as you pass, but your final reward will be heartaches and tears if you\'ve cheated the man in the glass',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\man-in-glass.md',
);

// Post content
$post_content = '<p><blockquote>When you get what you want in your struggle for self,<br>
and the world makes you king for a day,<br>
then go to the mirror and look at yourself,<br>
and see what that man has to say.<br><br></p>
<p>For it isn\'t your father, mother, or wife, <br>
whose judgement upon him must pass.<br>
the feller whose verdict counts most in your life<br>
is the man staring back from the glass.<br><br></p>
<p>He\'s the feller to please, never mind all the rest,<br>
for he\'s with you clear up to the end.<br>
And you\'ve passed your most dangerous, difficult test<br>
if the man in the glass is your friend.<br><br></p>
<p>You can fool the whole world down the pathway of years,<br>
and get pats on the back as you pass,<br>
but your final reward will be heartaches and tears<br>
if you\'ve cheated the man in the glass</blockquote></p>
<p>-Dale Wimbrow</p>
<p><img src="../../img/art/man-in-glass.jpg" width="400" alt="Man in the glass by Dale Wimbrow."></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>