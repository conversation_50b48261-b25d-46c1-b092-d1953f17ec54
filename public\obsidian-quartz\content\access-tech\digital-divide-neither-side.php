<?php
// Auto-generated blog post
// Source: content\access-tech\digital-divide-neither-side.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'My Personal Experience';
$meta_description = 'My Personal Experience In 2022, I encountered the digital divide firsthand. Despite being qualified, I lost a life-changing career opportunity as a pe...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'My Personal Experience',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'My Personal Experience In 2022, I encountered the digital divide firsthand. Despite being qualified, I lost a life-changing career opportunity as a pe...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\digital-divide-neither-side.md',
);

// Post content
$post_content = '<h2>My Personal Experience</h2>
<p>In 2022, I encountered the digital divide firsthand. Despite being qualified, I lost a life-changing career opportunity as a peer support specialist because I was immunocompromised during the pandemic. Vaya Health had "met their quota of virtual trainings" and refused to provide reasonable accommodations required by the Americans with Disabilities Act. They had a waiting list of able-bodied people and no incentive to help me participate virtually.</p>
<p>This experience inspired me to write about digital accessibility policies, though my recommendations have largely been ignored by decision-makers.</p>
<h2>Rethinking Our Understanding</h2>
<p>Neither textbook definitions nor my own truly capture what\'s at stake when we talk about the digital divide. Getting this wrong isn\'t just an academic problem—it has real consequences for people\'s lives.</p>
<p>Consider what I call the "Paradox of Technological Abundance":</p>
<p>- We assume those without digital access are worse off and need saving
- Meanwhile, we believe those of us with abundant access are better informed and more enlightened
- But is either assumption actually true?</p>
<h2>The Geographical Reality</h2>
<p>In my hometown near D.C., century-old zoning laws created literal divisions between white and Black neighborhoods—the train tracks forming a physical boundary that persists today. Similar divisions exist across America.</p>
<p>But here\'s what\'s interesting: information abundance doesn\'t automatically create wisdom.</p>
<h2>The Surprising Truth</h2>
<p>The "information-rich" side of the divide isn\'t necessarily thriving:</p>
<p>- Some powerful, wealthy individuals in positions of authority struggle with basic literacy
- Family members with unlimited access to information fall into conspiracy theory rabbit holes
- We don\'t fully understand how AI might erode critical thinking skills
- "Doomscrolling" appears to impair cognitive functioning</p>
<p>Meanwhile, I\'ve observed that members of marginalized communities often demonstrate superior media literacy despite limited access to technology.</p>
<h2>Moving Beyond Simple Solutions</h2>
<p>The traditional framing makes us focus on "saving" disadvantaged areas through technology and wealth while ignoring how information economies are failing everyone—just in different ways.</p>
<p>The uncomfortable truth? Neither side of this supposed divide is truly well-off. Access alone won\'t solve our problems if we don\'t address the quality of information and how we engage with it.</p>
<p>This isn\'t about having all the answers. It\'s about asking better questions about what meaningful digital inclusion should look like.</p>
<p>Badger, E., & Cameron, D. (2021, November 25). How railroads, highways and other man-made lines racially divide America’s cities. _The Washington Post_. https://www.washingtonpost.com/news/wonk/wp/2015/07/16/how-railroads-highways-and-other-man-made-lines-racially-divide-americas-cities/</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>