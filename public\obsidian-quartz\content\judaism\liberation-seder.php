<?php
// Auto-generated blog post
// Source: content\judaism\liberation-seder.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Liberation Seder Documentary';
$meta_description = 'On April 26, 2016, over 500 young American Jews risked arrest in five cities during Passover to declare "Dayenu! - Enough!" against American Jewish support for the occupation.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Liberation Seder Documentary',
  'author' => 'IfNotNow',
  'date' => '2016-04-26',
  'excerpt' => 'On April 26, 2016, over 500 young American Jews risked arrest in five cities during Passover to declare "Dayenu! - Enough!" against American Jewish support for the occupation.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\liberation-seder.md',
);

// Post content
$post_content = '<p><iframe width="560" height="315" src="https://www.youtube.com/embed/mg-lMMfWumI?si=XEkLGMh30p7GZ39N" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe># IfNotNow <a href="https://www.youtube.com/hashtag/liberationseder" class="external-link">#LiberationSeder</a> Documentary</p>
<h1>IfNotNow: Liberation Seder Documentary</h1>
<p><img src="https://yt3.ggpht.com/ytc/AIdro_ne1M_ch4KNPphrY9ztkKjepK3GaHTIvm_zpAKZexHZnQ=s88-c-k-c0x00ffffff-no-rj" alt="IfNotNow" width="200"></p>
<p><a href="https://www.youtube.com/@ifnotnow72" class="external-link">IfNotNow YouTube Channel</a>  
990 subscribers</p>
<p>---</p>
<h2>A Call for Liberation</h2>
<p>On April 26, 2016, over 500 young American Jews risked arrest in five cities during Passover to declare "Dayenu! - Enough!" against American Jewish support for the occupation. This historic event is captured in the IfNotNow Liberation Seder documentary, which you can watch <a href="https://www.youtube.com/watch?v=mg-lMMfWumI" class="external-link">here</a>.
<h3>Bringing Family to the Movement</h3></p>
<p>The documentary opens with a personal reflection, where an individual lights candles to symbolically include their family in California, both in spirit and politics, as they participate in the Liberation Seder. This act underscores the importance of carrying forward our ancestors\' dignity in the fight for collective liberation.
<h3>Challenging the Occupation</h3></p>
<p>The film highlights the urgent need to confront the moral crisis of occupation. It calls on Jewish leaders to acknowledge this crisis and join the fight for freedom and dignity for all, emphasizing that Jewish liberation cannot happen without Palestinian liberation.
<h3>Facing Resistance</h3></p>
<p>The documentary also addresses internal challenges, such as smear campaigns and censorship against those who speak out. It depicts the resilience of activists who refuse to be silenced and continue to demand justice.
<h3>A Generation\'s Promise</h3></p>
<p>The message is clear: this generation is committed to transforming the community\'s support for the occupation into a movement for universal freedom and dignity. The film ends with a powerful call to action: IfNotNow, when?</p>
<p>For more information, join IfNotNow\'s training or learn how you can help caption and translate this powerful video to reach a wider audience</p>
<p>- Edited by Rafael Shimunov. Clips of Israelis and Palestinians used under Creative Commons license from Sherez and Dana Shihadah.</p>
<p><em>Subtitles by the Amara.org community</em></p>
<p>---</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>