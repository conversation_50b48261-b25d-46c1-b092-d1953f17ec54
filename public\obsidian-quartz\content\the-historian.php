<?php
// Auto-generated blog post
// Source: content\the-historian.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Historian Excerpt';
$meta_description = 'It is with great regret that I imagine you, whoever you are, reading the account I must put down here. The regret is partly for myself---because I will surely be at least in trouble, maybe dead, or perhaps worse, if this is in your hands.';
$meta_keywords = 'A. A. Chips, blog, journal';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Historian Excerpt',
  'author' => 'Elizabeth Kostova',
  'date' => '2025-10-09',
  'excerpt' => 'It is with great regret that I imagine you, whoever you are, reading the account I must put down here. The regret is partly for myself---because I will surely be at least in trouble, maybe dead, or perhaps worse, if this is in your hands.',
  'tags' => 
  array (
    0 => 'journal',
  ),
  'source_file' => 'content\\the-historian.md',
);

// Post content
$post_content = '<p>My dear and unfortunate successor,</p>
<p>It is with great regret that I imagine you, whoever you are, reading the account I must put down here. The regret is partly for myself---because I will surely be at least in trouble, maybe dead, or perhaps worse, if this is in your hands. But my regret is also for you, my yet-unknown friend, because only by someone who needs such vile information will this letter someday be read. If you are not my successor in some other sense, you will soon be my heir---and I feel sorrow at bequeathing to another human being my own, perhaps unbelievable, experience of evil. Why I myself inherited it I don\'t know, but I hope to discover that fact; eventually---perhaps in the course of writing to you or perhaps in the course of further events.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>