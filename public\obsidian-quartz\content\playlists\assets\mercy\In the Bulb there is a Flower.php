<?php
// Auto-generated blog post
// Source: content\playlists\assets\mercy\In the Bulb there is a Flower.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'In the bulb, there is a flower';
$meta_description = 'E  In the bulb, there is a flower 	Fm  In the seed, and apple tree     B7 In cocoons, a hidden promise       E Butterflies will soon be free        A ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'In the bulb, there is a flower',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'E  In the bulb, there is a flower 	Fm  In the seed, and apple tree     B7 In cocoons, a hidden promise       E Butterflies will soon be free        A ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\mercy\\In the Bulb there is a Flower.md',
);

// Post content
$post_content = '<p>`E 
In the bulb, there is a flower
	F#m 
In the seed, and apple tree     B7 In cocoons, a hidden promise       E Butterflies will soon be free        A                E In the cold and snow of winter           F#m                 C#m there\'s a spring that waits to be      A                 E unrevealed until it\'s season           F#m  B         E something God alone can see              E There\'s a song in every silence         F#m Seeking word and melody           B7 There\'s a dawn in every darkness          E Bringing hope to you and me.           A                  E From the past will come the future           F#m        C#m What it holds, a mystery      A                 E unrevealed until it\'s season           F#m  B         E something God alone can see               E In our end is our beginning        F#m In our time, infinity         B7 In our doubt there is believing          E In our life, eternity        A              E In our death, a resurrection         F#m        C#m At the last a victory      A                   E unrevealed until it\'s season           F#m  B         E something God alone can see`</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>