<?php
// Auto-generated blog post
// Source: content\safeguarding\teen-parent-shifts.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Important Parenting Shifts You’ll Need When Your Child Becomes A Teenager';
$meta_description = 'Important Parenting Shifts You’ll Need When Your Child Becomes A Teenager - Ashley Waknine Youtube Video Notes';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Important Parenting Shifts You’ll Need When Your Child Becomes A Teenager',
  'author' => 'Ashley Waknine',
  'date' => '2022-08-12',
  'excerpt' => 'Important Parenting Shifts You’ll Need When Your Child Becomes A Teenager - Ashley Waknine Youtube Video Notes',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\safeguarding\\teen-parent-shifts.md',
);

// Post content
$post_content = '<p>Try to parent a teenager like a child and you will set yourself up for frequent power struggles. There are important parenting shifts that need to be made when your child becomes a teenager. Teenagers no longer feel like children. Most teenagers prefer to be treated like the adult they are becoming rather than the child-like self they feel ready to leave behind. 
<h2>Avoid remaining the primary decision maker and help them find what works for them.</h2></p>
<p>When children are little, parents have to be the primary decision maker for pretty much everything in their lives. Parents decide bedtimes, doctor’s appointments, mealtimes, which school they will attend and more. Usually, young children don’t mind their parents implementing these rules. However, this does not apply to teenagers.</p>
<p>Teenagers will need to and want to manage their own lives. While parents should maintain structure, you will need to shift from the role of the primary decision maker and help your teen find what works for them.</p>
<h2>Tackle resistance by shifting to a consultant role</h2>
<p> An important parenting shift you’ll need when your child becomes a teenager is shifting to a consultant role. Parents who refuse to step down from their role as the primary decision-maker, will ignite rebellion in their teens. If you micromanage your teen, they will try and break the rules you set because they want to feel a sense of control over their own lives.  Teens experience intense resistance to being told what to do and are often allergic to the advice given by parents. This is a developmental milestone designed to help them find themselves and become their own person. They will feel naturally inclined to do the opposite of what is expected/asked particularly if the pressure to do something exceeds their internal desire. </p>
<p>Therefore, it is better if parents give up their decision-maker role to the extent possible, and move to a role that is similar to that of a consultant/life coach. However, this does not mean that you have no say at all anymore and that you can’t enforce family rules. It means that you involve your teen in rule-making and consult with them about decisions. </p>
<h2>Maintain structures that provide quality time</h2>
<p>As children go into adolescence, they will naturally pull away from you as they are trying to find themselves. It’s important for you to try and remain close and involved in their life. You can achieve this by maintaining structures that provide quality time. Maintaining quality time is another important parenting shift you will need when your child becomes a teenager as your ability to parent and keep them safe is far easier when you are bonded together.</p>
<p>Some examples include:</p>
<p>-   Family dinners
-   Nightly walks
-   Car rides
-   An activity you both enjoy such as a specific sport</p>
<p>If you want more advice on the important parenting shifts you’ll need when your child becomes a teenager, visit our webpage on <a href="https://www.ashleywaknine.com/parent-coaching/" class="external-link">parent coaching</a>. We can help you learn methods and strategies to make your parenting more effective and address your own possible emotional struggles which may affect your parenting.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>