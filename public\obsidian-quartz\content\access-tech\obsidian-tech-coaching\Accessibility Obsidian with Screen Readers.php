<?php
// Auto-generated blog post
// Source: content\access-tech\obsidian-tech-coaching\Accessibility Obsidian with Screen Readers.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Steps to reproduce';
$meta_description = 'devinpraterhttps://forum.obsidian.md/u/devinprater Jun \'21https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669 "Post date" Hi ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Steps to reproduce',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'devinpraterhttps://forum.obsidian.md/u/devinprater Jun \'21https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669 "Post date" Hi ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\obsidian-tech-coaching\\Accessibility Obsidian with Screen Readers.md',
);

// Post content
$post_content = '<p><a href="https://forum.obsidian.md/u/devinprater" class="external-link">devinprater</a>
<a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669 "Post date"" class="external-link">Jun \'21</a></p>
<p>Hi all. I’m a totally blind person that’s interested in using Obsidian in my job, and maybe personally as well. My boss told me about Obsidian–that’s what he uses–so I decided to try it out. I found that some of it was usable with a screen reader (I tried <a href="https://www.nvaccess.org/" class="external-link">NVDA on Windows</a>. I found that when I navigated text that I’d written, using the arrow keys, all I heard was “blank” signifying that the screen reader could not read the text.</p>
<h3>Steps to reproduce</h3>
<p>Assuming NVDA is installed(get from link above):</p>
<p>-   Start NVDA.
-   Start Obsidian.  
    Close all open files, then create a new one. You can use arrow keys and Enter for this.
-   A file name box opens, and gains keyboard focus. The name of the field isn’t spoken with NVDA (much more minor bug). Enter any name, and press Enter.
-   Now, type some text. A heading, and then a paragraph, will do.
-   press the Up arrow a few times to get back to the heading.</p>
<h3>Expected result</h3>
<p>After pressing the arrow keys, NVDA should be speaking text behind the cursor, or the line that the cursor is on.</p>
<h3>Actual result</h3>
<p>NVDA speaks “blank” instead, meaning that NVDA sees no text at all. Interestingly, if I select all with Control + A, NVDA reads the text then. However, this is hardly a good editing experience.</p>
<h3>Environment</h3>
<p>-   Operating system: Windows 10</p>
<p>-   Obsidian version: v0.12.4
<h1><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669" class="external-link">Accessibility: Obsidian with screen readers</a></h1></p>
<p><a href="https://forum.obsidian.md/c/feature-requests/8" class="external-link">Feature requests</a></p>
<p><a href="https://forum.obsidian.md/tag/editor-legacy" class="external-link">editor-legacy</a><a href="https://forum.obsidian.md/tag/ui-ux" class="external-link">ui-ux</a><a href="https://forum.obsidian.md/tag/valuable" class="external-link">valuable</a><a href="https://forum.obsidian.md/tag/upstream-bug" class="external-link">upstream-bug</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/13 "Jun 2021"" class="external-link">Jun 2021</a></p>
<p>13 / 13</p>
<p>Mar 18</p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/13 "Mar 19"" class="external-link">Mar 19</a></p>
<p><a href="https://forum.obsidian.md/letter_avatar_proxy/v4/letter/d/67e7ee/90.png" class="external-link">![</a>](https://forum.obsidian.md/u/devinprater)</p>
<p><a href="https://forum.obsidian.md/u/devinprater" class="external-link">devinprater</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/4 "Post date"" class="external-link">Jun \'21</a></p>
<p>I just tried both Narrator on Windows and VoiceOver on the Mac. Narrator works even more poorly than NVDA, and while I can review the lines I’ve written with VoiceOver, I cannot hear what my cursor passes over. I’ll try JAWS next. I’m an accessibility tester as part of my job, so I have access to just about any screen reader you’d like me to test with.</p>
<p><a href="https://forum.obsidian.md/letter_avatar_proxy/v4/letter/d/67e7ee/90.png" class="external-link">![</a>](https://forum.obsidian.md/u/devinprater)</p>
<p><a href="https://forum.obsidian.md/u/devinprater" class="external-link">devinprater</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/5 "Post date"" class="external-link">Jun \'21</a></p>
<p>Okay, tried with JAWS and Orca (for Linux). JAWS did the same thing as VoiceOver, allowing me to review the text outside of the edit field, but I still cannot move my cursor there. The program didn’t work at all with Orca.</p>
<p><a href="https://forum.obsidian.md/user_avatar/forum.obsidian.md/whitenoise/90/11_2.png" class="external-link">![</a>](https://forum.obsidian.md/u/WhiteNoise)</p>
<p><a href="https://forum.obsidian.md/u/WhiteNoise" class="external-link">WhiteNoise</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/6 "Post date"" class="external-link">Jun \'21</a></p>
<p>Thank you very much for your testing. Your efforts prompted me to do some research. The issues you are highlighting are due to our editor library Codemirror 5.  
Codemirror 5 doesn’t play well with screen reader due to some architectural decisions and technical limitations and it won’t get “fixed”.</p>
<p><a href="https://github.com/codemirror/CodeMirror/issues/4604" class="external-link">github.com/codemirror/CodeMirror</a></p>
<p>#### <a href="https://github.com/codemirror/CodeMirror/issues/4604" class="external-link">Accessibility for blind and visually impaired screen reader users 9</a></p>
<p>opened  Feb 24, 2017</p>
<p>closed  Feb 25, 2017</p>
<p> <a href="https://forum.obsidian.md/uploads/default/original/3X/2/5/250c21847cb9887bab230cefd8bd181f429d88ce.png" class="external-link">![mjanusauskas</a> mjanusauskas](https://github.com/mjanusauskas)</p>
<p>The known issue of lack of compatibility with screen readers and assistive technology completely prevents blind and visually impaired screen reader...</p>
<p>Codemirror 6 is a complete rewrite of the editor and it does work with screenreaders. We already use Codemirror 6 in the mobile app and will eventually upgrade to Codemirror 6 in the desktop app as well.</p>
<p>3</p>
<p>3 MONTHS LATER</p>
<p><a href="https://forum.obsidian.md/letter_avatar_proxy/v4/letter/b/f4b2a3/90.png" class="external-link">![</a>](https://forum.obsidian.md/u/brocktaylor87)</p>
<p><a href="https://forum.obsidian.md/u/brocktaylor87" class="external-link">brocktaylor87</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/7 "Post date"" class="external-link">Sep \'21</a></p>
<p>This is great, I’m very much looking forward to the transition to CodeMirror 6 on desktop. Is there any rough timeline on when that transition might happen? I know of a few visually impaired users that are anxious to try out Obsidian (and I’ve told them all how great it is), so I’m excited about this update! !<a href="https://forum.obsidian.md/images/emoji/apple/smiley.png?v=10 ":smiley:"" class="external-link">:smiley:</a></p>
<p>1</p>
<p><a href="https://forum.obsidian.md/user_avatar/forum.obsidian.md/jbstep/90/1222_2.png" class="external-link">![</a>](https://forum.obsidian.md/u/jbstep)</p>
<p><a href="https://forum.obsidian.md/u/jbstep" class="external-link">jbstep</a>INSIDER</p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/8 "Post date"" class="external-link">Sep \'21</a></p>
<p>I’m interested too. I’d like to introduce it to my students who are visually impaired. It’s a great tool, but until accessiblity issues are addressed, it’s a no go.</p>
<p>1</p>
<p>2 MONTHS LATER</p>
<p><a href="https://forum.obsidian.md/letter_avatar_proxy/v4/letter/v/278dde/90.png" class="external-link">![</a>](https://forum.obsidian.md/u/vitallie)</p>
<p><a href="https://forum.obsidian.md/u/vitallie" class="external-link">vitallie</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/9 "Post date"" class="external-link">Nov \'21</a></p>
<p>I am on obsidian 0.12 as a blind user of the nvda screenreader.  
I would love to get in to obsidian, but am still not able to read text back while editing. Will this be fixed? How can we adres such a thing on the roadmap I mean accessibility should be a priority hthese days of all kinds of organisations. HOw can we get this properly adresed on the backlog? And fixed within one of the upcoming sprints?</p>
<p><a href="https://forum.obsidian.md/user_avatar/forum.obsidian.md/whitenoise/90/11_2.png" class="external-link">![</a>](https://forum.obsidian.md/u/WhiteNoise)</p>
<p><a href="https://forum.obsidian.md/u/WhiteNoise" class="external-link">WhiteNoise</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/10 "Post date"" class="external-link">Nov \'21</a></p>
<p>I am not 100% sure how things are now. 0.13 available to insiders now has the new editor. Let us know if it works better when you get chance.</p>
<p>2 MONTHS LATER</p>
<p><a href="https://forum.obsidian.md/letter_avatar_proxy/v4/letter/v/278dde/90.png" class="external-link">![</a>](https://forum.obsidian.md/u/vitallie)</p>
<p><a href="https://forum.obsidian.md/u/vitallie" class="external-link">vitallie</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/11 "Post date"" class="external-link">Jan 20</a></p>
<p>Seems wto work better, thnx.  
I enabled livepreview and now while my screenreader is in edit mode, I can read the text backwarts while editing in the same formfield.</p>
<p><a href="https://forum.obsidian.md/user_avatar/forum.obsidian.md/whitenoise/90/11_2.png" class="external-link">![</a>](https://forum.obsidian.md/u/WhiteNoise)</p>
<p><a href="https://forum.obsidian.md/u/WhiteNoise" class="external-link">WhiteNoise</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/12 "Post date"" class="external-link">Jan 21</a></p>
<p><a href="https://forum.obsidian.md/u/devinprater" class="external-link">@devinprater</a> Did you get the chance to test how the new editor in 0.13+ works with screen readers?</p>
<p>2 MONTHS LATER</p>
<p><a href="https://forum.obsidian.md/letter_avatar_proxy/v4/letter/b/3ab097/90.png" class="external-link">![</a>](https://forum.obsidian.md/u/bramd)</p>
<p><a href="https://forum.obsidian.md/u/bramd" class="external-link">bramd</a></p>
<p><a href="https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/13 "Post date"" class="external-link">Mar 19</a></p>
<p>I just did a quick test in version 0.13.33:</p>
<p>-   In source view, the current editor works quite well
-   In preview view, the editor has serious issues in NVDA when links/embedded elements are in the text. NVDA reads the text before the link and stops. The only way to read further is moving through the text until the cursor is on the link
-   The rest of the interface is not really accessible. For example, I can use ctrl+p to open the command palette, but while typing and arrowing through the list I can’t read which command is selected. Because many functions are available from this palette and I don’t think it would be so hard to make this accessible, this might be a good first step. Another app based on web technologies which implements such a command palette really well is Visual Studio Code, you might get some inspiration there.
-   Other parts of the UI such as the explorer are not accessible from the keyboard. Ensure everything that should be focusable (such as buttons/links/input controls) are reachable by tab or shift+tab. Of course, tab has another function in the editor, so you need a way to get out of the editor first. A few possible approaches:
    -   Introduce a hotkey to switch tab between inserting a tab in the editor or moving focus. Visual Studio Code does this and assigns ctrl+m by default
    -   Introduce a hotkey to move between UI panes, f6 is a common choice in Windows applications. This could move between the most important parts of the UI and get you out of the editor without using tab.</p>
<p>Hope this helps.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>