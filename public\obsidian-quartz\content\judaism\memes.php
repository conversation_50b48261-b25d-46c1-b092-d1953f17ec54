<?php
// Auto-generated blog post
// Source: content\judaism\memes.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = '!getout.jpg!onmyback.jpg !plantingtree.jpg!the-worlds-not-bad.jpg!standwith.jpg !1995.jpg !aznisht-zmirosproject.mp3 !beyondthewalls.jpg !federationle...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => '!getout.jpg!onmyback.jpg !plantingtree.jpg!the-worlds-not-bad.jpg!standwith.jpg !1995.jpg !aznisht-zmirosproject.mp3 !beyondthewalls.jpg !federationle...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\memes.md',
);

// Post content
$post_content = '
<p>![[getout.jpg]]![[onmyback.jpg]]
![[plantingtree.jpg]]![[the-worlds-not-bad.jpg]]![[standwith.jpg]]</p>
<p>![[1995.jpg]]</p>
<p>![[aznisht-zmirosproject.mp3]]
![[beyondthewalls.jpg]]</p>
<p>![[federationlevant.jpg]]</p>
<p>![[gauze.jpg]]
![[gazarubble.webp]]</p>
<p>![[helpbysafaaodah.jpg]]
![[IfIMustDie.jpg]]
[[Jewish for Palestine Index]]
![[interfaith.png]]
[[Invoke Article 6 and Expel the US from the United Nations]]
![[Issue23_WebCover_1024x1024.webp]]</p>
<p>![[keep-me-away.jpg]]
![[ladino.jpg]]
![[ladinoexpressionswjc.png]]
![[ladinoexpressionswjc2.png]]
![[landexpro.jpg]]
![[levantinianmelonflower.jpg]]</p>
<p>![[oyirnarishetsionist-danielkhanpsoykorolenko.mp3]]
[[Palestine Memes and Art]]
![[racialclassification.jpg]]
![[raintent.jpg]]
![[soul-of-our-soul.jpg]]</p>
<p>![[yom kippur.jpg]]</p>
<p>![[mosababutoha.jpg]]</p>
<p>![[myolivetree.jpg]]</p>
<p>![[palestine.jpg]]![[palestine-map.jpg]]</p>
<p>![[montaser.jpg]]</p>
<p>![[naksa.jpg]]</p>
<p>![[olivetrees.jpg]]</p>
<p>![[on-the-side-of-those-who-have-nothing.jpg]]</p>
<p>![[pexels-katra-7398258.jpg]]</p>
<p>![[seeds.jpg]]</p>
<p>![[smithsonianopenaccessstopthewar.jpg]]</p>
<p>![[southafrica.jpg]]</p>
<p>![[swanpal.jpg]]</p>
<p>![[VP-Hafrada-FINAL-20180926-02.jpg]]</p>
<p>![[VP-Hafrada-FINAL-20180926-03.jpg]]</p>
<p>![[VP-Hafrada-FINAL-20180926-04.jpg]]</p>
<p>![[VP-Hafrada-FINAL-20180926-05.jpg]]</p>
<p>![[VP-Hafrada-FINAL-20180926-06.jpg]]</p>
<p>![[VP-Hafrada-FINAL-20181025-01-Rev01.jpg]]</p>
<p>![[VP-HafradaSummary-FINAL-20180926-01.jpg]]</p>
<p>![[wearenot.jpg]]</p>
<p>![[yaya.png]]</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>