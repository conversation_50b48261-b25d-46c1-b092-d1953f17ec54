<?php
// Auto-generated blog post
// Source: content\climate\why-possums.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Why Possums';
$meta_description = 'Exploring the ecological importance of possums and personal connections to these misunderstood creatures';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Why Possums',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Exploring the ecological importance of possums and personal connections to these misunderstood creatures',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\why-possums.md',
);

// Post content
$post_content = '<p>![[possumfriends.png]]
<h1>Why Possums</h1></p>
<h2>Reflection Questions</h2>
- What personal experiences have you had with possums that sparked your interest in them?
- How do possums connect to broader themes of misunderstanding, resilience, and survival that appear in your other writings?
- What ecological benefits do possums provide that most people don\'t know about?
- How might possums serve as a metaphor for people who are misunderstood or marginalized?
<h2>Initial Thoughts</h2>
Possums are often misunderstood creatures that face unnecessary fear and persecution. They\'re actually beneficial animals that:
- Eat thousands of ticks (reducing Lyme disease)
- Are naturally resistant to rabies due to their low body temperature
- Help clean up ecosystems by consuming carrion
- Control pest populations like cockroaches, rats, and mice
<h2>Personal Connection</h2>
[Space to explore your personal connection to possums and why they\'re meaningful to you]
<h2>Advocacy Points</h2>
[Space to discuss how educating others about possums connects to broader environmental and social advocacy]
<h2>Related Content</h2>
- [[possumfriends.png]]
- [[summer/ptsd-myth/Bugs and Addiction]]
- [[Cultivating Community and Solidarity]]
- [[Rethinking Digital Ecosystems - A Call for Ecological Literacy in Tech]]
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>