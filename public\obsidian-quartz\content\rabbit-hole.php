<?php
// Auto-generated blog post
// Source: content\rabbit-hole.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'About A. A. Chips - Rabbit Hole of Wonders';
$meta_description = 'About A. A. Chips - Rabbit Hole of Wonders';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'About A. A. Chips - Rabbit Hole of Wonders',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'About A. A. Chips - Rabbit Hole of Wonders',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\rabbit-hole.md',
);

// Post content
$post_content = '<h1>About A. A. Chips - Rabbit Hole of Wonders</h1>
<h2>Personal Journey</h2>
<p>I am a writer, advocate, and creative who has experienced homelessness and rebuilt my life. Through this blog, I share my personal experiences, reflections, and advocacy work related to:</p>
<p>- Family alienation and reconnection</p>
<p>- Homelessness and housing insecurity</p>
<p>- Environmental and social justice</p>
<p>- Cultural identity and heritage</p>
<p>- Personal growth and healing</p>
<h2>Blog Purpose</h2>
  
This blog serves as both a personal expression project and a resource for others who may be experiencing similar challenges. By sharing my story and the lessons I\'ve learned, I hope to:
<p>1. Create understanding about the realities of homelessness and family alienation</p>
<p>2. Provide practical resources for those in similar situations</p>
<p>3. Build community around shared experiences</p>
<p>4. Advocate for systemic change</p>
<h2>Contact</h2>
<p>If you\'d like to connect, collaborate, or share your own story, please reach out through the <a href="/contact" class="external-link">contact form</a> or support this work through the <a href="/crowdfunding" class="external-link">crowdfunding campaign</a>.</p>
<p>Thank you for visiting and being part of this journey.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>