<?php
// Auto-generated blog post
// Source: content\playlists\assets\mercy\Where have all the flowers gone - The women of folk music.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'music #playlists';
$meta_description = 'music playlists Where have all the flowers gone? The women of folk music by Bijou Karman bijoukarman.com/publications.html !Pasted image 2025051021540...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'music #playlists',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'music playlists Where have all the flowers gone? The women of folk music by Bijou Karman bijoukarman.com/publications.html !Pasted image 2025051021540...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\mercy\\Where have all the flowers gone - The women of folk music.md',
);

// Post content
$post_content = '<p>#music #playlists</p>
<p>Where have all the flowers gone? The women of folk music
by Bijou Karman (bijoukarman.com/publications.html)</p>
<p>![[Pasted image 20250510215407.png|400]]</p>

<p>[[500 Miles - Mary Travers]]</p>
<p>![[500miles-marytravers.mp3]]</p>
<p>[[Blues Jumped The Rabbit - Karen Dalton]]</p>
<p>![[bluesjumpedtherabbit-karendalton.mp3]]</p>
<p>[[Cactus Tree - Joni Mitchell]]
![[cactustree-jonimitchell.mp3]]</p>
<p>[[Come to me Slowly - Margo Guryan]]</p>
<p>![[cometomeslowly-margoguryan.mp3]]</p>
<p>[[Dinks Song - Carolyn Hester]]
![[dinkssong-carolynhester.mp3]]</p>
<p>[[Helpless - Buffy Sainte Marie]]
![[helpless-buffystmarie.mp3]]</p>
<p>[[How Sad How Lonely - Connie Converse]]
![[howsadhowlovely-connieconverse.mp3]]</p>
<p>[[I\'d like to walk Around in Your Mind - Vashti Bunyan]]</p>
<p>![[idliketowalkaroundinyourmind-vashtibunyan.mp3]]</p>
<p>[[My Man on Love - Judee Sill]]
![[mymanonlove-judeesill.mp3]]</p>
<p>[[Someday I\'ll be a Farmer - Melanie Safka]]
![[somedayillbeafarmer-melaniesafka.mp3]]</p>
<p>[[There but for Fortune - Joan Baez]]
![[therebutforfortune-joanbaez.mp3]]</p>
<p>[[These Days - Nico]]
![[thesedays-nico.mp3]]</p>
<p>[[Who Knows Where the Time Goes - Judy Collins]]
![[whoknowswherethetimegoes-judycollins.mp3]]</p>
<p>[[With God on Our Side - Odetta]]
![[withgodonourside-odetta.mp3]]</p>
<p>[[Sibylle Baier - Tonight]]
![[tonight-sibyllebaier.mp3]]</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>