<?php
// Auto-generated blog post
// Source: content\writings\no-more-textbooks.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'school #april #writings #revisit';
$meta_description = 'school april writings revisit  --- Author:: April Cyr Date:: 3/25/2022 Key:: Private --- Please don\'t require us to buy textbooks for this class. In t...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'school #april #writings #revisit',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'school april writings revisit  --- Author:: April Cyr Date:: 3/25/2022 Key:: Private --- Please don\'t require us to buy textbooks for this class. In t...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\writings\\no-more-textbooks.md',
);

// Post content
$post_content = '<p>#school #april #writings #revisit</p>
<p>---
Author:: April Cyr
Date:: 3/25/2022
Key:: Private</p>
<p>---</p>
<p>Please don\'t require us to buy textbooks for this class.</p>
<p>In the past, I loved reading all sorts of materials. However, due to several traumatic brain injuries from accidents I\'ve survived, reading long blocks of text has become very difficult, along with dealing with loud noises and crowds. While I once cherished my large book collection, I now struggle with traditional reading.</p>
<p>Fortunately, we live in an age where information is widely accessible through various digital formats. Screen readers and narrated ebooks offer viable alternatives. While textbooks will likely always exist, I respectfully request that you reconsider mandatory textbook purchases for this course.</p>
<p>Often, the cost of textbooks can be substantial, and I personally find them heavy and cumbersome. If you are the author or receive royalties, I would even offer a $50 alternative payment to avoid the mandatory purchase of dense, 400-page textbooks filled with standardized information, especially when effective free online resources exist, like flash games for learning C# programming.</p>
<p>This isn\'t just a demand; I want to offer constructive alternatives:</p>
<p>- Only require textbooks with ebook options. If an ebook isn\'t available, please scan and provide essential sections in the online syllabus. Avoid requiring purchases for books used only once.
    
- Acknowledge that some specialized fields might rely on unique, less accessible sources like historical documents.
    
- Be open to students finding and sharing free learning tools. Often, students learning the material for the first time can discover resources that are more effective for beginners than expert-level publications. Experts sometimes struggle to teach introductory concepts effectively.
    
- For assignments referencing specific pages, consider providing photocopies or PDFs of those sections.
    
- Recognize that higher education is still expensive, and requiring an additional $150 for a textbook can be a significant burden, especially for first-generation college students from low-income backgrounds.</p>
<p>While some students will always purchase required materials, providing an option for students facing financial or accessibility barriers to succeed in your class would be greatly appreciated.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>