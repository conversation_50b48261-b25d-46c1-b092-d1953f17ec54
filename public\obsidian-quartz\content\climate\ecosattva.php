<?php
// Auto-generated blog post
// Source: content\climate\ecosattva.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Eco-Sattva Vows';
$meta_description = 'I vow to myself and to each of you: To commit myself daily to the healing of the world. And the welfare of all beings. To live on earth more lightly and less violently \\t\\tin the food, products, and energy I consume.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Eco-Sattva Vows',
  'author' => 'Joanna Macy & Chris Johnstone',
  'date' => '2025-10-09',
  'excerpt' => 'I vow to myself and to each of you: To commit myself daily to the healing of the world. And the welfare of all beings. To live on earth more lightly and less violently \\t\\tin the food, products, and energy I consume.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\ecosattva.md',
);

// Post content
$post_content = '<p><img src="../../img/edutainment/ecosattva.jpg" width="400" alt="from <em>Active hope: How to Face the Mess We\'re in without Going Crazy</em>, by Joanna Macy and Chris Johnstone."></p>
<p>from <em>Active hope: How to Face the Mess We\'re in without Going Crazy</em>, by Joanna Macy and Chris Johnstone</p>
<p><blockquote>"I vow to myself and to each of you:<br></p>
<p>To commit myself daily to the healing of the world. And the welfare of all beings.<br></p>
<p>To live on earth more lightly and less violently  in the food, products, and energy I consume.<br></p>
<p>To draw strength and guidance from the living Earth, the ancestors, the future generations, and my brothers and sisters of all species.<br></p>
<p>To support others in our work for the world and to ask for help when I need it.<br></p>
<p>To pursue a daily practice that clarifies the mind, strengthens my heart, and supports me in observing these vows."</blockquote></p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>