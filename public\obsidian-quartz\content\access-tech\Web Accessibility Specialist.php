<?php
// Auto-generated blog post
// Source: content\access-tech\Web Accessibility Specialist.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'april #vocation #writing #tech #webd #accessibility #revisit';
$meta_description = 'april vocation writing tech webd accessibility revisit --- Author:: April Cyr Date:: 10/14/2022 Key:: Public --- If you or somebody you know has a web...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'april #vocation #writing #tech #webd #accessibility #revisit',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'april vocation writing tech webd accessibility revisit --- Author:: April Cyr Date:: 10/14/2022 Key:: Public --- If you or somebody you know has a web...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\Web Accessibility Specialist.md',
);

// Post content
$post_content = '<p>#april #vocation #writing #tech #webd #accessibility #revisit</p>
<p>---
Author:: April Cyr
Date:: 10/14/2022
Key:: Public</p>
<p>---</p>
<p>If you or somebody you know has a website, that is a site of Public Accommodation. Under the Americans with Disabilities Act of 1990, it is mandatory to make reasonable efforts to accommodate users with various types of disability. These can be visual or auditory impairments, cognitive impairments, or fine motor skill impairments. While there is no accepted and defined legal guidelines under the ADA, rules for this are generally deferred to the Website Content Accessibility Guidelines (WCAG 2.0. 1990 was a long time ago, but the ADA itself was written in a robust way that allowed space for evolution. What does this all mean?</p>
<p>There is a framework of rules and best practices that are legally mandated for your website to be accessible. Not following this mandate can result in being the subject of a federal lawsuit. These have been steadily increasing year by year.</p>
<p>-If you hire a third party to build and maintain your website, there is no guarantee they are trained in this or are prepared to help with this.
-If you use tools like Wordpress or Squarespace, there is no guarantee your site is ADA Compliant. You can make your site compliant through those platforms but it still requires literacy and awareness.
-These standards exist for very good reason, and make websites better and more robust for User Experiences, especially for 26% of the population that has a disability, or relies on non-sighted or non-hearing browser features like keyboard navigation or screen reading. I\'d argue that this is to include low bandwidth users as well, but not getting into that today.
-There are widgets, applications, and softwares available that can audit and remediate some of these issues on your website. Even the most advanced Artificial Intelligence can only detect about 30% of non-compliance errors. Software that promises to fix this instantly generally doesn\'t deliver, and it\'s pretty expensive.
-The best practice for dealing with this, is having a trained human being read through your website and it\'s code and make the changes. The most ethical practice is picking a trained human being who is disabled to do this, and pay them to do this. 
-The average starting rate for Accessibility Specialists is 45$ an hour.
-I am training to be a Web Accessibility Specialist and feel confident enough answering your questions (ask me anything!) I\'m willing to help out with auditing and remediation! I don\'t need 45$ an hour but I am also not a charity worker either! I have been chronically unemployed for a long time, and really appreciate getting paid for my work! Questions are free! Tips are appreciated! Here are my payment plugs!</p>
<p>Thanks for reading! Stay safe and accessible! Not only is it the right thing to do, it is a legal mandate!</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>