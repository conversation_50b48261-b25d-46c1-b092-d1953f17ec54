<?php
// Auto-generated blog post
// Source: content\access-tech\ada-compliance.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Do I Need to Make My Website ADA Compliant?';
$meta_description = 'Does my Website Have to be ADA Compliant? Yes. A website is a place of Public Accommodation. Both Private Businesses and Government have to provide we...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Do I Need to Make My Website ADA Compliant?',
  'author' => 'Functional Lawyer',
  'date' => '2025-10-09',
  'excerpt' => 'Does my Website Have to be ADA Compliant? Yes. A website is a place of Public Accommodation. Both Private Businesses and Government have to provide we...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\ada-compliance.md',
);

// Post content
$post_content = '<h2>Does my Website Have to be ADA Compliant?</h2>
Yes. A website is a place of Public Accommodation. Both Private Businesses and Government have to provide website accessibility to their visitors.
<p>Your website has to be accessible for users that use assistive technology. Issues can arise from [lack of] color contrast between text and background. Inaccessible website is interpreted as active discrimination against people with disabilities. you are at risk of being sued. Lawsuits over ADA compliance have risen steadily year after year. A complaint will be written for one industry, which is first step of a lawsuit. Then it is very easy to swap out the name in the complaint. Typically we see one industry at a time being attacked. Lawsuits have risen sometimes 50% year after year. More and more businesses are starting since the pandemic, and have websites. Plaintiff attorneys know there is cash to be made. Be aware, this is a real concern. There are also ways to fix this and deter lawsuits. Business websites are considered places of public accommodation in the law. Ten new lawsuits per day in the country.</p>
<p>The worldwide standard is the WCAG 2.0. These standards are mandatory under other Federal Laws such as the Rehabilitation Act of 1973. the Air Carrier Access Act of 1986. There is no technical standard under ADA, WCAG is best we have. Follow WCAG 2.0 AA Standards.</p>
<p><a href="https://www.youtube.com/watch?v=UXlike1Qc_0" class="external-link">Does My Website Have to Be ADA Compliant? - YouTube</a>
<iframe width="560" height="315" src="https://www.youtube.com/embed/UXlike1Qc_0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe></p>
<p><!-- Images without Alt Text
Lack of Color Contrast
No clear or meaningful hierarchy
Links without proper descriptions where they go --></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>