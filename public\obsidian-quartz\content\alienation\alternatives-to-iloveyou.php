<?php
// Auto-generated blog post
// Source: content\alienation\alternatives-to-iloveyou.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Alternatives to \'I love you';
$meta_description = 'How to show you care without saying \'I love you';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Alternatives to \'I love you',
  'author' => 'Sue Ellson',
  'date' => '2025-10-09',
  'excerpt' => 'How to show you care without saying \'I love you',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\alienation\\alternatives-to-iloveyou.md',
);

// Post content
$post_content = '<p>“The most important thing in this world is to learn to give out love and let it come in.” ~Morrie Schwartz</p>
<p>As a child, I never heard the phrase “I love you.” Now, I hear people say it all the time—at the end of phone calls and whenever parting ways.</p>
<p>When I moved away from my hometown of Adelaide, South Australia, twenty years ago, I noticed how much less I felt loved interstate in Melbourne, Victoria. Even though I didn’t hear “I love you” when I was in Adelaide, somehow I knew people cared.</p>
<p>Soon after I arrived here, I had two wonderful children who’ve taught me all about love. They regularly tell me they love me, and I often overhear them telling their friends.</p>
<p>This got me thinking: how can we <a href="http://tinybuddha.com/blog/50-ways-to-show-you-care-without-spending-a-dime/" class="external-link">let people know we care</a>, beyond simply saying “I love you?”</p>
<p>I decided to make a list of some expressions that we can all say more often to family, friends, partners, and even colleagues. Perhaps you could use one of these each week for the next year.</p>
<p>- You are special to me.
- I feel amazing when I spend time with you.
- You give me goosebumps.
- I feel safe sharing my secrets with you.
- I accept you as you are.
- I understand how you feel.
- Is there anything I can do to help?
- I always have fun when I am with you.
- Please tell me how it is for you so I can understand.
- Can I hold your hand?
- Can I give you a hug?
- You inspire me.
- I really appreciate it when you…
- You are one of the most amazing gifts I have ever received.
- I value everything you’ve taught me.
- The insights you have shared mean the world to me.
- Your thoughtfulness is a delight to receive.
- I will never forget how you…
- I feel so relaxed and happy when you…
- Seeing you when … happened made it all okay.
- I can feel it when your heart sings because it makes my heart sing too.
- I could sit next to you and not say anything and be at peace.
- The way you handled … showed me that you are truly…
- Your comments about … helped me enormously.
- I’m thankful to have you in my life.
- I could go anywhere with you.
- I believe your intentions for me are always good, even when I cannot understand what you do.
- I trust you.
- I can go outside of my comfort zone with you.
- Knowing you gives me courage.
- The world is less scary when I am with you.
- I appreciate that your suggestions help me make difficult choices.
- I lose all concept of time when I am with you.
- If something serious happened to me, you’re the first person I would call.
- You are so generous in spirit.
- Surprise me more often because I like your surprises.
- I love how you … whenever I need to …
- I hear your voice even when we are not in the same place.
- I feel connected to you even when I cannot see you.
- Your wisdom has saved me.
- I feel refreshed and renewed around you.
- I enjoy your sense of humor.
- Whenever I see a photo of us together, I smile.
- I appreciate that you think about my feelings before you do and say things.
- Your smile makes me smile.
- I love that you know me so well.
- When I think about you, I often remember when you…
- I want to keep you in my past, present, and future.
- I can be me when I am with you—I hope you feel the same way.
- Circumstance brought us together; choice keeps us together.
- You are so lovable.
- I love you.</p>
<p>I know that the positive feedback I’ve received in the past has kept me going during the darkest moments of my life.</p>
<p>I hope that by saying “I love you” in many different ways, the special people in your life will have good memories that can sustain them during the more difficult moments in their lives.</p>
<p>How do you let people know you love them?</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>