<?php
// Auto-generated blog post
// Source: content\next-door-home.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The House Next Door - September 7, 2025';
$meta_description = 'The House Next Door - September 7, 2025 The favorite neighbor\'s house next door is empty now. They left in a hurry this week, a quiet exodus. I didn\'t...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The House Next Door - September 7, 2025',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'The House Next Door - September 7, 2025 The favorite neighbor\'s house next door is empty now. They left in a hurry this week, a quiet exodus. I didn\'t...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\next-door-home.md',
);

// Post content
$post_content = '<h3>The House Next Door - September 7, 2025</h3>
<p>The favorite neighbor\'s house next door is empty now. They left in a hurry this week, a quiet exodus. I didn\'t piece together why they had the moving truck there that evening. After six years of living next to each other. I got the news of why. Lewis is gone. He died over a week ago. He would have been 25. I had no idea.</p>
<p>We were good neighbors. With very different lives. We enjoyed quiet through our shared walls. I\'d smell Chicken Adobo cooking at times walking by the porch. The cadence of Spanish laughter. Their young daughter was one of the biggest fans of my apple chips. We looked out for each other when some of the teenagers in the neighborhood were breaking into unlocked cars and stealing our things. In the past year we had two very small spats, that I remember with a hot pinch of shame, both were quickly resolved and put aside.</p>
<p>One spat was about my roommate smoking in the house, which traveled through our shared vent system. Maria was upset when this would happen, rightfully so, and Emily would be upset about the threat of losing the autonomy to self-medicate indoors.</p>
<p>The other spat was Lewis on New Years. I was woken up at 3 AM to the sound of the police knocking on the door. Not on my door, but on the one next to mine. Lewis was plastered drunk, crying, in handcuffs. He had gotten into a fight, and the police were called. He didn\'t have his key, and his Sister wasn\'t home. He was crying that he wanted his Bobby, their dog, and to go to sleep. The police didn\'t know if he actually lived there. I wasn\'t happy to be woken up by this, but went downstairs, and told them, yes, he does live there. I could have lied and said he didn\'t, and he would have had to sober up in a holding cell overnight and probably learn a lesson that way.</p>
<p>Lewis had been overall making good decisions with his life. He incorporated his business and moved into a single apartment around the corner. You could say he had turned a corner. Back when I lived with Mat, a man recovering from alcoholism who is a sponsor with AA, Lewis would look up to him as inspiration and what is possible.</p>
<p>The EMTs arrived too late and couldn\'t bring Lewis back.</p>
<p>His Sister couldn\'t stand staying at that house another day. They had been wanting to make a move. But the pain of looking out the window hoping to see him walk down to visit was too much. I get it, I have to look at the spot in the parking lot my dog was hit by a car every day. But my financial reality keeps me here. They will be okay, and I will miss being their neighbor.</p>
<p>The thing is, I have a very personal and vivid memory of Lewis that has lived under my skin for almost six years, and I need to let it out.</p>
<p>-------------------------------------
<h3>October 19th, 2019</h3></p>
<p>It was a cold rainy October night in 2019, the 19th to be exact.  the kind of cold that arrives sudden and mean. I was new, alone, and the house felt too big.</p>
<p>A frantic knocking at the window started. Desperate, the sound of skin slapping against glass.</p>
<p>A man’s face, pale and blurred by rain, was staring in. A stranger. My heart was a trapped bird. _He’s trying to get in._ He was begging, his words slurred into a single plea: _LetmeinletmeinletmeinI’msocold._</p>
<p>What would you do? Would you let a stranger banging at your window in? I tried offering blankets and a rain jacket. That would not cut it.</p>
<p>I opened the porch door, he\'s already trying to climb into my window. So I go and help him in. He\'s missing his phone, and just has a consulate ID on him. Great. I don\'t want to call emergency services and get someone deported.</p>
<p>Trying to stand, he fell and clunked his head. Not a serious injury, but a testament to his inebriation. I have no idea what he was on. But I could smell the alcohol.</p>
<p>He was my neighbor, and got locked out forgetting his key while no one was home that evening. Lewis had a habit of going on benders and not having his keys or phone. I didn\'t know any of that then.</p>
<p>I got him a hot drink, dry blankets. His clothes still soaking wet. I got some thermal layers of mine and offered our empty bedroom for privacy to change. I cuddled him with the warmth of my body. In that moment, he was just a boy, who was very scared.</p>
<p>I give him some space to change into dry warm clothes. He calls me to come in when he is done. We talk. I sit next to him. He attempts to pull me into kissing him. I push him away. It was a revolting, clumsy gesture. Probably not about attraction, but profound loneliness. He saw me as a man, which I wasn\'t at the time, and was a total stranger. I had no interest in that.</p>
<p>The rejection flashed across his face, and then something worse: nothing. His eyes rolled back. He stopped breathing. In the absolute silence of my house, I believed I killed him.</p>
<p>I ran for my phone, ready to shatter my own rule and call 911, ready to face any consequence. And then I heard it—a ragged, glorious gasp for air from the room above. He was back.</p>
<p>We found his friend on Facebook. The call was made. The car arrived. He left as suddenly as he had arrived, taking the secret of that night with him.</p>
<p>For years afterwards, Lewis could not look me in the eye. He spoke to my roommate, a recovering alcoholic, about his hopes to get clean, but to me, he was a ghost. I was the guardian of his deepest shame. Maybe he was closeted. I don\'t know and it isn\'t my business. Either way I cared about him and supported him from a distance of familial strangeness.</p>
<p>I don\'t wear a pride flag on my sleeve. I don\'t live openly about who I am to everyone. It\'s a choice I make for survival and privacy. We both kept secrets to stay safe. My quiet privacy, maybe it could have been a bridge instead of a wall. Maybe my silence felt like a door closed in his face. I will never know.</p>
<p>Now that is now mine alone to carry. The house next door is empty. The window is just a window. And that cold October rain keeps falling, forever, in the space between.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>