<?php
// Auto-generated blog post
// Source: content\music\family-tree.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Em _ D _ G _ F#m _ _ _ D _ _  ';
$meta_description = 'Em  D  G  Fm    D      A  CD Before the days of Jell-O, A lived a prehistoric fellow, who loved a maid and courted her beneath the D banyan tree. An...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Em _ D _ G _ F#m _ _ _ D _ _  ',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Em  D  G  Fm    D      A  CD Before the days of Jell-O, A lived a prehistoric fellow, who loved a maid and courted her beneath the D banyan tree. An...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\music\\family-tree.md',
);

// Post content
$post_content = '<p>Em _ D _ G _ F#m _ _ _ D _ _  </p>
<p>_ A _ C#D Before the days of Jell-O, A lived a</p>
<p>prehistoric fellow, who loved a maid and courted her beneath</p>
<p>the D banyan tree. And they had lots of G#m children, and A their</p>
<p>children all had children, and they kept on F#m having children G until one</p>
<p>A of them D had me. _ We\'re a Bm family,</p>
<p>AG and we\'re D a tree, our A roots Bm go</p>
<p>deep E down in A history. G My great A-great</p>
<p>D-granddaddy, reaching up F#m to me, D we\'re a G green D and</p>
<p>A growing D family tree. _ _ _  </p>
<p>GD My _ A _ _ D _ _ _  </p>
<p>_ grandpa came from Russia, A my grandma came from</p>
<p>Prussia, they met in Nova Scotia, had my dad in D Tennessee. </p>
<p>Then they moved to Yokohama, A where daddy met my</p>
<p>mama. Her dad\'s from Alabama, G and her A mom\'s D part</p>
<p>Cherokee. _ We\'re a Bm family, AG and</p>
<p>we\'re D a tree, our A roots Bm go deep E down</p>
<p>in A history. G From my great A-great D-granddaddy,</p>
<p>reaching F#m up to me, D we\'re a green and A growing</p>
<p>D family tree. _ _ _ G _ D _  </p>
<p>_ A _ _ D _ _ _ BD Well,</p>
<p>one fine day I may go, A to Tierra del Fuego. Perhaps</p>
<p>I\'ll meet my wife there, and we\'ll move to D Timbuktu. And our</p>
<p>kid will be G bilingual, A and though she may stay single, she could</p>
<p>of course co-mingle G with the A king of D _ Kathmandu. </p>
<p>Cause we\'re a Bm family, F#mG and we\'re D a</p>
<p>tree, our A roots Bm go deep E down in A history. </p>
<p>From G my great A-great D-granddaddy, reaching up F#m to</p>
<p>me, D we\'re a G green D and A growing D family</p>
<p>tree. _ _ _ A _ G _ _ D _  </p>
<p>_ _ A _ Bm _ A _ E _ _ A _  </p>
<p>_ G _ _ A _ _ D _ _ F#m _  </p>
<p>_ D _ G _ D _ _ A _ _ D _  </p>
<p>_ The folks in Madagascar aren\'t A the same</p>
<p>as in Alaska. They got different foods, different moods, and</p>
<p>different colored D skin. You may have a different A name, but</p>
<p>underneath we\'re much the same. You\'re probably my cousin, G and the whole</p>
<p>A world D is our kin. _ We\'re a Bm family,</p>
<p>G and we\'re D a tree, our F#m roots Bm go</p>
<p>deep E down in A history. From G my great A-great</p>
<p>D-grandmother, reaching up to me, we\'re a G green D and</p>
<p>growing AD family tree. We\'re a G green Bm and</p>
<p>A growing _ D family. _ _ _</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>