<?php
// Auto-generated blog post
// Source: content\kitchen\Kytchyn.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'I remember my first time cooking. I was 11 and I tried to make rice. Nobody else was home. There were specific instructions. I never read too well.. I...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'I remember my first time cooking. I was 11 and I tried to make rice. Nobody else was home. There were specific instructions. I never read too well.. I...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\Kytchyn.md',
);

// Post content
$post_content = '<p>I remember my first time cooking. I was 11 and I tried to make rice. Nobody else was home. There were specific instructions. I never read too well.. I added the rice to boiling water. With some butter and salt the rice turned out fine. And I always cooked it that way, and got better and better at my improper cooking. </p>
<p>At age 24 I remember being chased out of a significant other’s apartment for trying to cook rice the wrong way, at 3 in the morning. I was berated for my incompetence and slept in my vehicle hungry that morning. That kytchyn was a war zone and the center of verbal attacks and control inflicted when I stepped out of place. </p>
<p>This was not isolated. Just two years before I was kicked out of both my parents houses for composting. I lived mainly out of my car since. I have a peaceful den in there where I’m not disturbed. Only tragedy in the houselessness is being cut off from my lifeblood of qooking.</p>
<p>I ran away to another state after J20. I didn’t fully understand why, but I knew it was a life or death situation. With the distance and a year and a half I came out as trans female fungi. Trans female is easier to convey than calling myself a mushroom. I seek to personify the traits of mushrooms that challenge the powers of growth and drive ecological restoration and remediation, and am more comfortable doing that as female. My sexuality has broadened over the years but has intimately included my foodcraft.</p>
<p>I’ve always used food magick as a means of earning respect from people. Since I came out that has been a tactic of survival. I refuse to die in my twenties or thirties. I refuse to settle for reviled degenerate. I refuse to be destroyed. I make magic happen out of the kytchyn. Regardless of the human love and relationships I will live to experience, nothing will protect and honor me the way food magick does. It’s my lifelong love and I will fight for it.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>