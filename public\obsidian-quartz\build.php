<?php
/**
 * Build Script for Obsidian-Quartz Blog
 * Converts Markdown files to PHP files for the blog system
 */

// Include configuration
require_once __DIR__ . '/path-helper.php';
require_once __DIR__ . '/includes/image-helper.php';
$config = include __DIR__ . '/config.php';
$paths = initPaths($config, __FILE__);

class MarkdownToPHPBuilder {
    private $config;
    private $paths;
    private $contentDir;
    private $templatePath;
    private $allPosts = []; // Store all posts for category indexes

    public function __construct($config, $paths) {
        $this->config = $config;
        $this->paths = $paths;
        $this->contentDir = $paths['base_path'] . 'content/';
        $this->templatePath = $paths['template_path'];

        // Initialize ImageHelper with base path
        ImageHelper::init($paths['base_path']);
    }

    /**
     * Build all markdown files to PHP
     */
    public function buildAll() {
        echo "Starting build process...\n";

        $markdownFiles = $this->findMarkdownFiles($this->contentDir);
        $totalFiles = count($markdownFiles);

        echo "Found {$totalFiles} markdown files to process.\n";

        // First pass: collect all posts data
        $this->allPosts = $this->collectAllPostsData($markdownFiles);

        // Second pass: convert files with access to all posts data
        foreach ($markdownFiles as $index => $mdFile) {
            $progress = $index + 1;
            echo "[{$progress}/{$totalFiles}] Processing: {$mdFile}\n";



            try {
                $this->convertMarkdownToPhp($mdFile);
                echo "  ✅ Success\n";
            } catch (Exception $e) {
                echo "  ❌ Error: " . $e->getMessage() . "\n";
            }
        }

        echo "\nBuild complete!\n";
    }

    /**
     * Collect data from all posts for category indexes
     */
    private function collectAllPostsData($markdownFiles) {
        $allPosts = [];

        foreach ($markdownFiles as $mdFile) {
            try {
                $content = file_get_contents($mdFile);
                if ($content === false) continue;

                $relativePath = str_replace($this->contentDir, '', $mdFile);
                $relativePath = ltrim($relativePath, '/\\');

                // Skip index files for this collection
                if (basename($mdFile) === 'index.md') {
                    continue;
                }

                $parsed = $this->parseMarkdown($content);
                $frontmatter = $parsed['frontmatter'];

                // Determine URL path
                $urlPath = str_replace('.md', '.php', $relativePath);
                
                // Extract category from path
                $category = '';
                $pathParts = explode('/', $relativePath);
                if (count($pathParts) > 1) {
                    $category = $pathParts[0];
                }

                $allPosts[] = [
                    'title' => $frontmatter['title'] ?? 'Untitled',
                    'author' => $frontmatter['author'] ?? $this->config['site']['author'],
                    'date' => $frontmatter['date'] ?? date('Y-m-d'),
                    'excerpt' => isset($frontmatter['excerpt']) && !empty($frontmatter['excerpt'])
                        ? $frontmatter['excerpt']
                        : $this->generateExcerpt($parsed['raw_content']),
                    'tags' => $frontmatter['tags'] ?? [],
                    'category' => $category,
                    'url' => $urlPath,
                    'thumbnail' => $frontmatter['thumbnail'] ?? null,
                    'source_file' => $relativePath
                ];

            } catch (Exception $e) {
                // Skip files that can't be parsed in collection phase
                continue;
            }
        }

        // Sort posts by date (newest first)
        usort($allPosts, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        return $allPosts;
    }

    /**
     * Find all markdown files recursively
     */
    private function findMarkdownFiles($dir) {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'md') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * Convert a single markdown file to PHP
     */
    private function convertMarkdownToPhp($mdFilePath) {
        try {
            $content = file_get_contents($mdFilePath);
            if ($content === false) {
                throw new Exception("Failed to read file");
            }

            $relativePath = str_replace($this->contentDir, '', $mdFilePath);
            $relativePath = ltrim($relativePath, '/\\');
            $phpFilePath = str_replace('.md', '.php', $mdFilePath);

            // Parse frontmatter and content
            $parsed = $this->parseMarkdown($content);

            // Check if this is a category index file
            $isCategoryIndex = (basename($mdFilePath) === 'index.md' && 
                               dirname($relativePath) !== '.');

            // Generate PHP file content
            $phpContent = $this->generatePhpFile($parsed, $relativePath, $isCategoryIndex);

            // Write PHP file
            if (file_put_contents($phpFilePath, $phpContent) === false) {
                throw new Exception("Failed to write output file");
            }

        } catch (Exception $e) {
            // Re-throw with more context
            throw new Exception($e->getMessage() . " (in " . basename($mdFilePath) . ")");
        }
    }

    /**
     * Parse markdown content and frontmatter
     */
    private function parseMarkdown($content) {
        $frontmatter = [];
        $markdownContent = $content;

        // Extract frontmatter if present
        if (preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
            $frontmatterText = $matches[1];
            $markdownContent = $matches[2];

            // Parse YAML-like frontmatter
            $lines = explode("\n", $frontmatterText);
            foreach ($lines as $line) {
                if (preg_match('/^(\w+):\s*(.+)$/', trim($line), $lineMatches)) {
                    $key = $lineMatches[1];
                    $value = trim($lineMatches[2], '"\'');

                    // Handle arrays (tags, categories)
                    if (in_array($key, ['tags', 'categories'])) {
                        // Check if it's in array format [item1, item2, item3]
                        if (preg_match('/^\[(.*)\]$/', $value, $arrayMatches)) {
                            $frontmatter[$key] = array_map('trim', explode(',', $arrayMatches[1]));
                        }
                        // Check if it's comma-separated
                        elseif (strpos($value, ',') !== false) {
                            $frontmatter[$key] = array_map('trim', explode(',', $value));
                        }
                        // Single value - make it an array
                        else {
                            $frontmatter[$key] = [$value];
                        }
                    } else {
                        $frontmatter[$key] = $value;
                    }
                }
            }
        }

        // If no title found in frontmatter, try to extract from content
        if (!isset($frontmatter['title']) || empty($frontmatter['title'])) {
            $frontmatter['title'] = $this->extractTitleFromContent($markdownContent);
        }

        // Convert markdown to HTML
        $htmlContent = $this->markdownToHtml($markdownContent);

        return [
            'frontmatter' => $frontmatter,
            'content' => $htmlContent,
            'raw_content' => $markdownContent
        ];
    }

    /**
     * Simple markdown to HTML converter
     */
    private function markdownToHtml($markdown) {
        // Basic markdown conversion
        $html = $markdown;

        // Process image references FIRST (before other markdown processing)
        $html = ImageHelper::processImageReferences($html, $this->paths['images_path']);

        // Headers
        $html = preg_replace('/^# (.+)$/m', '<h1>$1</h1>', $html);
        $html = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $html);
        $html = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $html);

        // Bold and italic
        $html = preg_replace('/\*\*(.+?)\*\*/', '<strong>$1</strong>', $html);
        $html = preg_replace('/\*(.+?)\*/', '<em>$1</em>', $html);

        // Links
        $html = preg_replace('/\[([^\]]+)\]\(([^)]+)\)/', '<a href="$2" class="external-link">$1</a>', $html);

        // Paragraphs
        $paragraphs = explode("\n\n", $html);
        $html = '';
        foreach ($paragraphs as $p) {
            $p = trim($p);
            if (!empty($p) && !preg_match('/^<[h1-6]/', $p) && !preg_match('/^<div class="dynamic-image-container"/', $p)) {
                $html .= "<p>$p</p>\n";
            } else {
                $html .= "$p\n";
            }
        }

        return $html;
    }

    /**
     * Generate PHP file content
     */
    private function generatePhpFile($parsed, $relativePath, $isCategoryIndex = false) {
        try {
            $frontmatter = $parsed['frontmatter'];
            $content = $parsed['content'];

            // Determine path prefix based on directory depth
            $depth = substr_count($relativePath, '/');
            $pathPrefix = str_repeat('../', $depth);

            // Extract metadata with safe defaults
            $title = isset($frontmatter['title']) ? $frontmatter['title'] : 'Untitled';
            $author = isset($frontmatter['author']) ? $frontmatter['author'] : $this->config['site']['author'];
            $date = isset($frontmatter['date']) ? $frontmatter['date'] : date('Y-m-d');
            $excerpt = isset($frontmatter['excerpt']) && !empty($frontmatter['excerpt'])
                ? $frontmatter['excerpt']
                : $this->generateExcerpt($parsed['raw_content']);

            // Ensure tags is always an array
            $tags = isset($frontmatter['tags']) ? $frontmatter['tags'] : [];
            if (!is_array($tags)) {
                // If it's a string, try to parse it
                if (is_string($tags)) {
                    // Check for array format [item1, item2]
                    if (preg_match('/^\[(.*)\]$/', $tags, $matches)) {
                        $tags = array_map('trim', explode(',', $matches[1]));
                    }
                    // Check for comma-separated
                    elseif (strpos($tags, ',') !== false) {
                        $tags = array_map('trim', explode(',', $tags));
                    }
                    // Single tag
                    else {
                        $tags = [$tags];
                    }
                } else {
                    $tags = [];
                }
            }
        } catch (Exception $e) {
            throw new Exception("Error generating PHP file: " . $e->getMessage());
        }

        // Generate PHP content
        $php = "<?php\n";
        $php .= "// Auto-generated blog post\n";
        $php .= "// Source: " . str_replace('/', '\\', $relativePath) . "\n\n";

        // Path helper and config loading with robust fallback logic
        $php .= "// Load path helper and configuration\n";
        $php .= "// Try multiple possible locations for path-helper.php\n";
        $php .= "if (file_exists(__DIR__ . '/../path-helper.php')) {\n";
        $php .= "    require_once __DIR__ . '/../path-helper.php';\n";
        $php .= "    \$config = include __DIR__ . '/../config.php';\n";
        $php .= "} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {\n";
        $php .= "    require_once __DIR__ . '/../../path-helper.php';\n";
        $php .= "    \$config = include __DIR__ . '/../../config.php';\n";
        $php .= "} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {\n";
        $php .= "    require_once __DIR__ . '/../../../path-helper.php';\n";
        $php .= "    \$config = include __DIR__ . '/../../../config.php';\n";
        $php .= "} else {\n";
        $php .= "    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);\n";
        $php .= "}\n";
        $php .= "\$paths = initPaths(\$config, __FILE__);\n\n";

        // Page variables
        $php .= "// Page variables\n";
        $php .= "\$page_title = " . var_export($title, true) . ";\n";
        $php .= "\$meta_description = " . var_export($excerpt, true) . ";\n";
        $php .= "\$meta_keywords = " . var_export(implode(', ', array_merge([$this->config['site']['author'], 'blog'], $tags)), true) . ";\n";
        $php .= "\$css_path = \$paths['css_path'];\n";
        $php .= "\$js_path = \$paths['js_path'];\n";
        $php .= "\$base_url = \$paths['base_path'];\n";
        $php .= "\$thumbnail = null;\n";
        $php .= "\$related_posts = [];\n";
        $php .= "\$random_posts = [];\n";
        $php .= "// This will be populated by the sidebar include\n\n";

        // Post metadata
        $php .= "// Post metadata\n";
        $php .= "\$post_data = " . var_export([
            'title' => $title,
            'author' => $author,
            'date' => $date,
            'excerpt' => $excerpt,
            'tags' => $tags,
            'source_file' => str_replace('/', '\\', $relativePath)
        ], true) . ";\n\n";

        // Post content
        $php .= "// Post content\n";
        $php .= "\$post_content = " . var_export($content, true) . ";\n\n";

        // For category index pages, add content_posts array
        if ($isCategoryIndex) {
            $category = dirname($relativePath);
            // Extract just the category name (remove 'content\' prefix if present)
            if (strpos($category, 'content\\') === 0) {
                $category = substr($category, 8); // Remove 'content\' (8 characters)
            } elseif (strpos($category, 'content/') === 0) {
                $category = substr($category, 8); // Remove 'content/' (8 characters)
            }
            $php .= $this->generateContentPostsArray($category);
        }

        // Template inclusion
        $php .= "// Generate dynamic content\n";
        $php .= "ob_start();\n";
        $php .= "?>\n";

        $php .= "<article class=\"post-header\">\n";
        $php .= "    <?php if (isset(\$post_data['title'])): ?>\n";
        $php .= "        <h1 class=\"post-title\"><?php echo htmlspecialchars(\$post_data['title']); ?></h1>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "    \n";
        $php .= "    <?php \$metadata = []; ?>\n";
        $php .= "    <?php if (isset(\$post_data['author'])): ?>\n";
        $php .= "        <?php \$metadata[] = '<span class=\"post-author\"><i class=\"icon-user\"></i>By ' . htmlspecialchars(\$post_data['author']) . '</span>'; ?>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "    <?php if (isset(\$post_data['date'])): ?>\n";
        $php .= "        <?php \$formatted_date = (strtotime(\$post_data['date']) !== false) ? date('F j, Y', strtotime(\$post_data['date'])) : htmlspecialchars(\$post_data['date']); ?>\n";
        $php .= "        <?php \$metadata[] = '<span class=\"post-date\"><i class=\"icon-calendar\"></i>' . \$formatted_date . '</span>'; ?>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "    \n";
        $php .= "    <?php if (!empty(\$metadata)): ?>\n";
        $php .= "        <div class=\"post-meta\">\n";
        $php .= "            <?php echo implode(' | ', \$metadata); ?>\n";
        $php .= "        </div>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "    \n";
        $php .= "    <?php if (isset(\$post_data['tags'])): ?>\n";
        $php .= "        <?php \$tags = is_array(\$post_data['tags']) ? \$post_data['tags'] : [\$post_data['tags']]; ?>\n";
        $php .= "        <?php if (!empty(\$tags)): ?>\n";
        $php .= "            <div class=\"post-tags\">\n";
        $php .= "                <span class=\"tags-label\">Tags:</span>\n";
        $php .= "                <?php foreach (\$tags as \$tag): ?>\n";
        $php .= "                    <?php \$tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim(\$tag)))); ?>\n";
        $php .= "                    <a href=\"tag-<?php echo \$tag_slug; ?>.html\" class=\"tag\"><?php echo htmlspecialchars(\$tag); ?></a>\n";
        $php .= "                <?php endforeach; ?>\n";
        $php .= "            </div>\n";
        $php .= "        <?php endif; ?>\n";
        $php .= "    <?php endif; ?>\n";
        $php .= "</article>\n\n";
        $php .= "<div class=\"post-content\">\n";
        $php .= "    <?php echo \$post_content; ?>\n";
        $php .= "</div><!-- .post-content -->\n";

        // For category index pages, include the post grid AFTER the content
        if ($isCategoryIndex) {
            $php .= $this->generatePostGridTemplate();
        }

        $php .= "<?php\n";
        $php .= "\$content = ob_get_clean();\n\n";
        $php .= "// Include template\n";
        $php .= "include \$paths['template_path'];\n";
        $php .= "?>";

        return $php;
    }

    /**
     * Generate content_posts array for category index pages
     */
    private function generateContentPostsArray($category) {
        $php = "// Posts for this category\n";
        $php .= "\$content_posts = [];\n\n";

        $php .= "// All available posts data\n";
        $php .= "\$all_posts_data = " . var_export($this->allPosts, true) . ";\n\n";

        $php .= "// Filter posts for this category\n";
        $php .= "foreach (\$all_posts_data as \$post) {\n";
        $php .= "    // Check if post URL is in this category directory\n";
        $php .= "    if (isset(\$post['url']) && strpos(\$post['url'], 'content\\\\' . " . var_export($category, true) . " . '\\\\') === 0) {\n";
        $php .= "        \$content_posts[] = \$post;\n";
        $php .= "    } elseif (isset(\$post['category']) && \$post['category'] === " . var_export($category, true) . ") {\n";
        $php .= "        \$content_posts[] = \$post;\n";
        $php .= "    }\n";
        $php .= "}\n\n";

        return $php;
    }

    /**
     * Generate post grid template for category index pages
     */
    private function generatePostGridTemplate() {
        return <<<'HTML'
<?php if (!empty($content_posts)): ?>
<div class="post-grid">
    <?php foreach ($content_posts as $post_item): ?>
        <div class="post-card">
            <a href="<?php
                // Convert full path to relative path for category index pages
                $url = $post_item['url'] ?? '#';
                if (strpos($url, 'content\\') === 0) {
                    // Extract just the filename from the full path
                    $url = basename($url);
                }
                echo htmlspecialchars($url);
            ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post_item['thumbnail']) && $post_item['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post_item['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post_item['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post_item['title'] ?? ''); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post_item['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post_item['title'] ?? ''); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post_item['title'] ?? ''); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post_item['title'] ?? ''); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post_item['excerpt'] ?? '') ? $post_item['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post_item['author']) && $post_item['author'] && is_string($post_item['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post_item['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post_item['date']) && $post_item['date']): ?>
                            <span class="post-date"><?php echo is_string($post_item['date']) ? htmlspecialchars($post_item['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post_item['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post_item['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </a>
        </div>
    <?php endforeach; ?>
</div>
<?php else: ?>
    <p>No posts found in this category.</p>
<?php endif; ?>

HTML;
    }

    /**
     * Extract title from markdown content when frontmatter is missing
     */
    private function extractTitleFromContent($content) {
        // Try to find the first heading
        if (preg_match('/^#+\s*(.+)$/m', $content, $matches)) {
            return trim($matches[1]);
        }

        // Try to find the first line that looks like a title (not empty, not tags)
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            $line = trim($line);
            // Skip empty lines, tag lines, and lines that start with special characters
            if (empty($line) ||
                strpos($line, '#') === 0 ||
                strpos($line, '!') === 0 ||
                strpos($line, '[') === 0 ||
                strpos($line, '---') === 0) {
                continue;
            }

            // If we find a reasonable line, use it as title
            if (strlen($line) > 3 && strlen($line) < 100) {
                return $line;
            }
        }

        return 'Untitled';
    }

    /**
     * Generate excerpt from content
     */
    private function generateExcerpt($content, $length = 150) {
        // Remove markdown formatting
        $text = preg_replace('/[#*\[\]()_`]/', '', $content);
        $text = preg_replace('/\n+/', ' ', $text);
        $text = trim($text);

        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }
}

// Command line usage
if (php_sapi_name() === 'cli') {
    $builder = new MarkdownToPHPBuilder($config, $paths);
    $builder->buildAll();
} else {
    // Web interface
    echo "<h1>Obsidian-Quartz Build Script</h1>";
    echo "<p>This script converts Markdown files to PHP files for the blog.</p>";
    echo "<p>Run from command line: <code>php build.php</code></p>";

    if (isset($_GET['build'])) {
        echo "<pre>";
        $builder = new MarkdownToPHPBuilder($config, $paths);
        $builder->buildAll();
        echo "</pre>";
    } else {
        echo "<p><a href='?build=1'>Click here to run build process</a></p>";
    }
}