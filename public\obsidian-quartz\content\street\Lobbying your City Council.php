<?php
// Auto-generated blog post
// Source: content\street\Lobbying your City Council.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Roles (one person can do multiple roles):';
$meta_description = 'To schedule: call or email your councilmember\'s office, identify yourself as a constituent, and ask to meet with your city councilmember for 30 minute...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Roles (one person can do multiple roles):',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'To schedule: call or email your councilmember\'s office, identify yourself as a constituent, and ask to meet with your city councilmember for 30 minute...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\street\\Lobbying your City Council.md',
);

// Post content
$post_content = '<p>To schedule: call or email your councilmember\'s office, identify yourself as a constituent, and ask to meet with your city councilmember for 30 minutes to discuss your issue. Prepare a factsheet. Sample language from other city council resolutions. Consider other supporting materials.</p>
<p>Do you have any neighbors you can invite to join you for the meeting? More constituents your councilmember hears from, more likely to say yes when introduced with a resolution. Can bring in special expertise or unique point of view, faith leaders, civic group leaders, professors.</p>
<p>Roles (one person can do multiple roles):
+ Leader: leads introductions, gives meeting overview, closes meeting, keeps things on track
+ Expert: Someone who can relay facts and why there is an urgent need for action
+ Storyteller: Delivers message, makes it personal, speaks to why council should act
+ Ask: Makes hard ask and is prepared to respond
+ Notetaker: takes comprehensive notes and shares with group
+ Timekeeper: makes sure meeting ends on time
+ followup: leaves materials the group may have</p>
<p>When you arrive to the office, introduce yourself and explain you have an appointment</p>
<p>Suggested Agenda (30 minutes):
+ Introductions (1min) - very brief; names and hometowns
+ Meeting overview (1min) Why are you here? What do you want to talk about?
+ Issue (10 min) -
	+ Problem: what it means for your community and state
	+ Solution: why your solution is the only guaranteed way to solve problem
	+ urgency/opportunity: why endorse a ban? why is councilmember right person to introduce resolution
+ Ask (1min) - yes or no: will your councilmember introduce resolution? why or why not?
+ q&a: (5min) - answer questions
+ info gathering (5min) - Have they heard from anyone on this issue? who? is there anyone they need to hear from? How quickly can they introduce the resolution: who do they think would vote for it? who do they think wouldn\'t? How can you help them get it passed?
+ Closing (1min) - thank them for time and agree on follow up plan.</p>
<p>After meeting:
email your councilmember in 1-2 days and thank them for their time, include follow up materials with your email and reiterate timeline for action</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>