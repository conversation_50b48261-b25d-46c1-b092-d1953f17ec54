<?php
// Auto-generated blog post
// Source: content\access-tech\List of Workplace Accommodations.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'ada #disability #vocational #accessibility #autism #april #writings #resources #revisit';
$meta_description = 'ada disability vocational accessibility autism april writings resources revisit --- Author:: April Cyr Date:: 3/1/2022 Key:: Public --- If your busine...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'ada #disability #vocational #accessibility #autism #april #writings #resources #revisit',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'ada disability vocational accessibility autism april writings resources revisit --- Author:: April Cyr Date:: 3/1/2022 Key:: Public --- If your busine...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\List of Workplace Accommodations.md',
);

// Post content
$post_content = '<p>#ada #disability #vocational #accessibility #autism #april #writings #resources #revisit</p>
<p>---
Author:: April Cyr
Date:: 3/1/2022
Key:: Public</p>
<p>---</p>
<p>If your business is serious about Accessibility, here is some advice. Don\'t require proof of a disability and HR in order to implement reasonable accommodations. Have a list of the features you can offer by request and have that list available and visible in a high traffic area. If you suspect fraud, waste, or abuse, then ask for proof and HR to get involved.</p>
<p>+ Accessible Parking
If possible, this can also look like shaded spots.
+ Ramp-based Access and Elevators
+ Single Person/Nongendered/Handicap Accessible Bathrooms
+ Gentle Lighting and Unlit Workspaces
+ No Fragrance
+ Software Vouchers
+ Text-Based Participation in Meetings
+ Noise Canceling Headphones
+ Sitting-Friendly
+ Keeping Fidget Toys on hand
+ Wear what is comfortable and presentable. 
+ We can keep medications and emergency protocols for seizure and other related conditions
+ Communication Boards in the Office
+ Encourage sharing pronouns on email signatures
+ There is a bathroom with a shower and toiletries and towel
+ Accessible Menu Items for Dietary Restrictions if there is a Company Cafeteria
+ Quiet Work Rooms
+ Daycare Program for Children & Pets
+ Work from Home Options when Viable</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>