<?php
// Auto-generated blog post
// Source: content\access-tech\Three Responses to Fake news Online.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Three responses to fake news online';
$meta_description = 'Three responses to fake news online By April Cyr 1/24/2024  Echo Chambers and Bad Information on the Internet For the uninitiated in information discr...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Three responses to fake news online',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Three responses to fake news online By April Cyr 1/24/2024  Echo Chambers and Bad Information on the Internet For the uninitiated in information discr...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\Three Responses to Fake news Online.md',
);

// Post content
$post_content = '<h1>Three responses to fake news online</h1>
<p>By April Cyr
1/24/2024</p>
<h2>Echo Chambers and Bad Information on the Internet</h2>
<p>For the uninitiated in information discretion, the world wide web can be a place of conspiracies, echo chambers and wacky thinking. Even the best of us are vulnerable to this. If you do not know somebody who has been lost to echo chambers on social media, I guarantee you know somebody who has lost a family member to mis- and dis-information on the internet. When I say dangerous, I am not talking hyperbole. Wars can be sparked by bad information. There is an old proverb that.. ‘a lie can travel around the world twice before the truth can put pants on.’ There are support forums (subreddits) on reddit.com dedicated to <a href="https://www.reddit.com/r/QAnonCasualties/wiki/index/" class="external-link">combating this trend such as r/Qanoncasualties</a>.</p>


<p>The stakes are high in 2024. Here are three strategies that can be implemented that may help neutralize the spread of bad information and promote good hygiene on the internet:</p>

<h3>Why does this matter for Designers?</h3>
<p>Platforms may be held legally liable if, let’s say the unmoderated flow of bad information leads to a bunch of people committing federal crimes. In 2024, deliberately bad information is a threat to everybody and as designers it’s important to be able to spot and understand what is happening. If you design websites with comments, forums, user post-a-bility.. It may be on you to ensure there are adequate mechanisms for moderation. Be the socially responsible design you wish to see in the world..</p>

<p>#### Additional Sources:</p>
<p>- <a href="https://www.wired.com/story/a-dangerous-new-home-for-online-extremism/" class="external-link">A Dangerous New Home for Online Extremism | WIRED</a>
    
- <a href="https://nordvpn.com/blog/information-warfare/" class="external-link">What is information warfare? With real examples | NordVPN</a>
    
- <a href="https://www.businessinsider.com/qanon-tiktok-family-conspiracy-theories-community-2022-6" class="external-link">They lost their families to conspiracy theories. Now they\'re finding others just like them on TikTok</a></p>
<h2>Digital Media Literacy Curricula in Finland</h2>
<p>A success story in combatting mis- and dis-information is Finland, who has incorporated into their grade school curriculum Digital Media Literacy, from a young age. Kids in school are taught how to spot bad, fake news information. And as a result, Finland is a healthier and more enlightened society. This is a positive trend in education to make the web better and safer. American public education (and Higher Education) need to implement these lessons in curricula.</p>
<p>One app which has been designed to help build Digital Media Literacy in a fun (maybe also disturbing way) is called <a href="https://trollfactory.yle.fi/" class="external-link">Troll Factory</a>. You can play the app directly on the site. In the app you act as the role of an anonymous employee whose boss has ordered you to spread anti-immigration content online for (_revenue..? Clicks..? infamy..??_)</p>
<p>#### Learn more at the links below:</p>
<p>- <a href="https://www.nationalobserver.com/2023/05/16/news/finland-visionary-fight-disinformation-teaches-citizens-question-online" class="external-link">Finland’s ‘visionary’ fight against disinformation teaches citizens to question what they see online | Canada\'s National Observer</a>
    
- <a href="https://www.ucsusa.org/resources/disinformation-same-misinformation" class="external-link">Is Disinformation the Same as Misinformation? | Union of Concerned Scientists</a>
    
- <a href="https://kavi.fi/en/media-education/" class="external-link">National AudioVisual Institute in Finland - Find free curriculum resources here</a></p>

<p>Here are two web-based trends you can implement as an individual user or designer that will improve the quality of your research and data: Markdown and RSS Feeds.</p>
<h2>Markdown: What’s the Hype About?</h2>
<p>A writing syntax for the web, focused on simplicity, and ease of learning. A method to catalog every article, every interesting post, every piece of text-based web content, and keep it in one location for easy access. Organizing all that data on bite sized text files that store easily. And if you are reading this, you likely already know 99% of how to write in it.</p>

<p>In the best of cases with Markdown repositories, you have a library of great web content. In the worst of cases with Markdown repositories, you have networks of mis and disinformation documented.</p>

<p>Sometimes it is easier to spot bunk information when we revisit it, instead of passively consuming and letting it disappear into the digital ether. Markdown is a powerful documentation tool that can be used in many different applications to improve the quality of data across the web. There are many websites operating today that run in Markdown, such as <a href="http://www.wikipedia.org" class="external-link">Wikipedia</a> and <a href="http://www.reddit.com" class="external-link">Reddit</a>.</p>

<p>If I haven’t sold you on trying Markdown yet, imagine a Markup language that is 99% easier to learn than HTML 🙃.</p>
<p>#### To learn more about the basics of Markdown, check out the resources below 👇</p>
<p>- <a href="https://www.markdownguide.org/getting-started/" class="external-link">Getting Started | Markdown Guide</a>
    
- <a href="https://www.howtogeek.com/448323/what-is-markdown-and-how-do-you-use-it/" class="external-link">What Is Markdown, and How Do You Use It?</a>
    
- <a href="https://wpengine.com/resources/using-markdown-wordpress/" class="external-link">Using Markdown in WordPress</a>
    
- <a href="https://www.ericholscher.com/blog/2016/mar/15/dont-use-markdown-for-technical-docs/" class="external-link">Why You Shouldn’t Use “Markdown” for Documentation — Eric Holscher</a> (here’s a counterargument against my claim that Markdown is really good for documentation..)
<h2>RSS Feeds: Beating the Algorithm at the Terminal</h2></p>
<p>RSS Feeds are almost as old as Internet Blogs. Both first showed up around the late nineties, and have weathered the test of time. Unfortunately, many are not educated to the power and ease of access RSS can bring to the internet. They can be powerful in combating what are considered echo chambers and social media algorithms.</p>
<p>By aggregating all of the content you follow in one place, you can curate and control that feed 100%. It will save lots of time as well, preventing you from having to visit each site individually. Otherwise your feeds will be based on what can manipulate you into clicking and engagement for likes and third party revenue streams. With RSS, content can be kept informative and thought provoking.</p>
<p>Stay organized, save time, and find new content that actually consistently engage your interests.</p>
<p>You can embed an RSS Feed right into your Wordpress site/page.</p>
<p>- <a href="https://www.wpbeginner.com/beginners-guide/what-is-rss-how-to-use-rss-in-wordpress/" class="external-link">What Is RSS? How to Use RSS in WordPress</a>
- <a href="https://blog.hubspot.com/website/wordpress-rss-feed" class="external-link">11 Best WordPress RSS Feed Plugins in 2023 (& How to Find Your WordPress RSS Feed)</a>
- <a href="https://wordpress.org/plugins/wp-rss-aggregator/" class="external-link">WP RSS Aggregator – News Feeds, Autoblogging, Youtube Video Feeds and More – WordPress plugin</a></p>
<h2>Conclusion</h2>
<p>The internet can be a dangerous place due to algorithms, fake news, and disinformation. This is a bad trend with disastrous consequences for the future of not only the internet, but also democracy, humanity, and the fate of the world.</p>

<p>Some countries have begun to implement Digital Media Literacy programs in school curriculums, meant ot teach kids and young adults how to spot B%S, and understand the civic duty of maintaining good information technology on the web. This is something for Web Developers and Designers to be acutely aware of, and consider in design, as well as for local and state governments to fund programs for.</p>

<p>Two technologies that give us as users and designers better control over information flow are Markdown, and RSS Feeds. By investing a small amount of time to learn these tools, the quality of your projects and internet use will be dramatically better.</p>
<p>##</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>