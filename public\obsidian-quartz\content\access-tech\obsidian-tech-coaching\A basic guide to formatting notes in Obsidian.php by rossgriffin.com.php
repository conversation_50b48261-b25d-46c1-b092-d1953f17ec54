<?php
// Auto-generated blog post
// Source: content\access-tech\obsidian-tech-coaching\A basic guide to formatting notes in Obsidian.md by rossgriffin.com.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '[A basic guide to formatting notes in Obsidian.md](https://rossgriffin.com/tutorials/obsidian-basics-guide/)';
$meta_description = 'A basic guide to formatting notes in Obsidian.md rossgriffin.comhttps://rossgriffin.com/tutorials/obsidian-basics-guide/  A basic guide to formatting ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '[A basic guide to formatting notes in Obsidian.md](https://rossgriffin.com/tutorials/obsidian-basics-guide/)',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'A basic guide to formatting notes in Obsidian.md rossgriffin.comhttps://rossgriffin.com/tutorials/obsidian-basics-guide/  A basic guide to formatting ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\obsidian-tech-coaching\\A basic guide to formatting notes in Obsidian.md by rossgriffin.com.md',
);

// Post content
$post_content = '<p><a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/" class="external-link">A basic guide to formatting notes in Obsidian.md (rossgriffin.com)</a></p>
<h1><a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/" class="external-link">A basic guide to formatting notes in Obsidian.md</a></h1>
<p>![](https://i0.wp.com/rossgriffin.com/wp-content/uploads/2021/08/Obsidian-Graph.jpg?fit=800%2C409&ssl=1)</p>
<p><a href="https://rossgriffin.com/" class="external-link">Home</a> » <a href="https://rossgriffin.com/blog/" class="external-link">Blog</a> » <a href="https://rossgriffin.com/category/tutorials/" class="external-link">Tutorials</a> » A basic guide to formatting notes in Obsidian.md</p>
<p>-   <a href="https://rossgriffin.com/" class="external-link">Ross Griffin</a>
-   August 30, 2021</p>
<p>#### Recent Posts</p>
<p><a href="https://rossgriffin.com/self-improvement/a-day-of-rest-self-reflection-and-appreciation/" class="external-link">A Day of Rest, Self-Reflection and Appreciation</a></p>
<p>March 2, 2022</p>
<p><a href="https://rossgriffin.com/productivity/my-obsidian-personal-knowledge-management-system-walk-through/" class="external-link">My Obsidian Personal Knowledge Management System – Walk Through</a></p>
<p>February 5, 2022</p>
<p><a href="https://rossgriffin.com/essays/why-i-decided-to-start-a-degree-at-age-25/" class="external-link">Why I decided to start a degree at age 25</a></p>
<p>February 5, 2022</p>
<p><a href="https://rossgriffin.com/self-improvement/running/running-calculator/" class="external-link">Running Calculator</a></p>
<p>February 2, 2022</p>
<p><a href="https://rossgriffin.com/misc/no-switchy/" class="external-link">No Switchy</a></p>
<p>January 21, 2022</p>
<p>[](https://rossgriffin.com/self-improvement/a-day-of-rest-self-reflection-and-appreciation/)</p>
<p>[](https://rossgriffin.com/productivity/my-obsidian-personal-knowledge-management-system-walk-through/)</p>
<p>[](https://rossgriffin.com/essays/why-i-decided-to-start-a-degree-at-age-25/)</p>
<p>[](https://rossgriffin.com/self-improvement/running/running-calculator/)</p>
<p>[](https://rossgriffin.com/misc/no-switchy/)</p>
<p><strong>Download a FREE obsidian.md reference guide:</strong></p>
<p>Success! Now check your email to confirm your subscription.</p>
<p>Obsidian.md is a <a href="https://www.markdownguide.org/getting-started/" class="external-link">markdown</a> text editor. It is different from other markdown editors because it has jumped on the _linked thought_ band wagon. “Linked Thought” refers to a group of note taking applications that allow you to seamlessly link thoughts and notes together. It is like having your very own wikipedia. The applications go much further than note taking. Applications like <a href="https://obsidian.md/" class="external-link">Obsidian.md</a> and <a href="https://roamresearch.com/" class="external-link">Roam Research</a> are spearheading a <a href="https://en.wikipedia.org/wiki/Knowledge_management#:~:text=Knowledge%20management%20(KM" class="external-link">knowledge management</a>%20is%20the,the%20best%20use%20of%20knowledge.&text=KM%20is%20an%20enabler%20of%20organizational%20learning.) revolution. People have used it to write academic papers and novels. People also use it to support their own work: everyone from software developers to lawyers are seeing the value in the idea of _linked thought_.</p>
<p>If you are unfamiliar with markdown it can be tricky to get started with obsidian. This article is meant to be a quick reference guide on the basics of Obsidian and the Markdown specific to obsidian. It is aimed at beginners and people who are unfamiliar with markdown.</p>
<p>Most Obsidian Tutorials start with how to link pages together, this doesn’t make any sense. While this is one of the big selling points of Obsidian it can be a confusing topic for someone that is just starting out. My approach will be to explain obsidian as a text editing tool, and then we’ll add “linked thought” at a later stage as the icing on the cake. You can use the contents menu to jump to a section you want to read more about.</p>
<p>Here is a walkthrough of <a href="https://rossgriffin.com/tru/" class="external-link">my Obsidian.md personal knowledge management system</a>.</p>
<h2>Basic Text formatting</h2>
<p>Like Microsoft Word or Apple pages Obsidian allows you to perform some basic text editing like making text: Bold, Italic, Strike Through and highlighted.</p>
<p>---</p>
<p><strong>This is some bold text</strong>:</p>
<p>```
<strong>This is some bold text</strong>
```</p>
<p>---</p>
<p>_This is italicized text_</p>
<p>```
<em>This is italicized text</em>
```</p>
<p>---</p>
<p>~~This text has a strikethrough~~</p>
<p>```
~~This text has a strike through~~
```</p>
<p>---</p>
<p>This is highlighted</p>
<p>```
==This is highlighted==
```</p>
<p>---</p>
<p>Block quotes are a good way of indicating that you’re quoting someone, or to call attention to specific text:</p>
<p>![](https://i0.wp.com/rossgriffin.com/wp-content/uploads/2021/08/Block-Quote.jpg?resize=800%2C58&ssl=1)</p>
<p>```
> Block Quote
```</p>
<p>---</p>
<h2>Headings and Horizontal Rules</h2>
<p>In Obsidian you can add headings:</p>
<p>![](https://i0.wp.com/rossgriffin.com/wp-content/uploads/2021/08/Headings-Example.jpg?resize=671%2C269&ssl=1)</p>
<p>```
<h1>Heading 1</h1>
<h2>Heading 2</h2>
<h3>Heading 3</h3>
#### Heading 4
##### Heading 5
###### Heading 6
```</p>
<p>---</p>
<p>Another helpful text editing tool is the horizontal rule.</p>
<p>---</p>
<p>Use 3 dashes for a horizontal rule: `<strong>---</strong>`  
<strong>One more note on headings and horizontal rules:</strong> you can put 3 dashes underneath text to make it a heading like this:</p>
<p>```
some text
---
```</p>
<p>This will display as a heading 1</p>
<h2>Lists and Checklists</h2>
<p>In Obsidian you can create ordered lists, unordered lists and check lists:</p>
<h3>Ordered list</h3>
<p>1.  This
2.  is an
3.  Ordered list</p>
<p><strong>The Markdown:</strong></p>
<p>```
1. This
2. is an
3. Ordered list
```</p>
<h3>Unordered List</h3>
<p>-   This
-   is an
-   unordered list</p>
<p><strong>The Markdown:</strong></p>
<p>```
- This
- is an
- unordered list
```</p>
<h3>Checklist</h3>
<p>-   This
-   is a
-   checklist</p>
<p><strong>The Markdown:</strong></p>
<p>```
- [x] This
- [ ] is a
- [ ] checklist
```</p>
<h2>Code Blocks</h2>
<p>Code blocks are useful for two reasons: one, the code is not compiled in your editor. Two, the code will in most cases have proper syntax highlighting.</p>
<p>![](https://i0.wp.com/rossgriffin.com/wp-content/uploads/2021/08/HTML-example.jpg?resize=800%2C175&ssl=1)</p>
<p>To insert a code block use the “` followed by the programming language you want to use. For example:</p>
<p><strong>The Markdown:</strong></p>
<p>````
```html
some code here
```
````</p>
<h2>Tables</h2>
<p>Obsidian allows you to insert tables into text:</p>
<p>Heading</p>
<p>Description</p>
<p>Header</p>
<p>Title</p>
<p>Paragraph</p>
<p>Text</p>
<p><strong>The Markdown:</strong></p>
<p>```
| Syntax      | Description |
| ----------- | ----------- |
| Header      | Title       |
| Paragraph   | Text        |
```</p>
<p>Tables are a bit tricky to work with in markdown so I’d recommend downloading the plugin “Advanced Tables” which makes editing tables a more pleasant experience.</p>
<h2>Footnotes</h2>
<p>Footnotes are great if you want to add something to your notes without breaking the flow of your writing. The mark up would look like this:</p>
<p>```
Text with foot note[^1]</p>
<p>[^1]: Footnote
```</p>
<p><strong>Tip:</strong> I’d highly recommend the plugin: “Footnote Shortcut” if you are going to be using footnotes on a regular basis.</p>
<h2>Linking</h2>
<h3>Linking and back Linking</h3>
<p>The most basic way to link in obsidian is the wiki style link. This is an in-text link to another page in your obsidian vault. You can achieve this by using square brackets like so: <strong>[[Page Link]]</strong></p>
<p>You can also link to specific blocks by adding a “^” symbol after your page name like so: <strong>[[Page Link^block to link to]]</strong>. When you do this, Obsidian will bring up a context menu to assist you in choosing the correct block in your document. You can link to other pages in your obsidian vault or you can use this to link to blocks in the current document. This is helpful for creating page contents for large documents.</p>
<p>Explaining back links in text is a bit difficult so I have made a video below to demonstrate how this works.</p>
<h3>Adding Aliases</h3>
<p>Aliases are a way to link to your files with different names. For example, you want to link to a page called “Productivity Home” this page contains all your important research on productivity. If you wanted to use this text in a natural sentence it would be tricky. In come Aliases: using an aliase you can refer to this page like so “[[Productivity Home|<strong>Productivity</strong>]] is a topic that too many people have an opinion about, but few have truly mastered.” The bolded text is what will display in your document. You can find a more clear explanation of this in the video below:</p>
<h3>Linking to External Locations</h3>
<p>You can link to websites and files on your computer by using external links. They’re different from internal links. External links look like this: <strong><a href="[https://placetolinkto.com](https://placetolinkto.com/" class="external-link">Text</a>)</strong></p>
<h2>Embedding content</h2>
<p>Embedding content is another thing that makes _linked thought_ software so powerful. In Software like Obsidian.md and Roam Research you can link to other pages or blocks. This is powerful because you can show an entire page within a page, or just a paragraph or two.</p>
<p>This is great because when the content is updated on the original page it is also updated everywhere it’s embedded. People have found many creative uses for this feature.</p>
<p>To embed a single page use this syntax: <strong>![[Page Name]]</strong> Notice how it’s the same as linking to page except you just put the exclamation mark in front?</p>
<p>You can embed just a paragraph by using the same syntax but, you’ll need to include the “^” symbol after the page name like so: <strong>![[Page Name^block to link to]]</strong></p>
<h3>Embedding images and other file types.</h3>
<p>You can embed media in your Obsidian documents. You’ll need to make sure that the media exists in the vault folder first. Many people like to create an attachemnts folder and keep all their media there. Once you’ve put your media in the obsidian folder you can link to it like this: ![[picture.jpg]]</p>
<p>Here is a list of file types you can embed in obsidian:</p>
<p>1.  Markdown files: `md`;
2.  Image files: `png`, `jpg`, `jpeg`, `gif`, `bmp`, `svg`;
3.  Audio files: `mp3`, `webm`, `wav`, `m4a`, `ogg`, `3gp`, `flac`;
4.  Video files: `mp4`, `webm`, `ogv`;
5.  PDF files: `pdf`.</p>
<p>[Video] – Coming Soon</p>
<h2>Queries and Search</h2>
<p>Queries allow you to find several notes in your vault that match a specific criteria. This is helpful if you want to create a hub for specific notes. For example, you could tag all notes derived from videos, and then query your vault so only the notes from a specific creator are shown:</p>
<p>````
```query
#video + Tiago Forte
```
````</p>
<p>In my vault this will show me all notes on videos by the creator Tiago Forte</p>
<p>[Video] – Coming Soon</p>
<h2>Adding Meta Data</h2>
<p>You can add additional data to your notes such as tags and aliases. Metadata uses a markup called YAML which stands for “Yet another markup language”.</p>
<p>YAML metadata is useful if you want to add tags to your notes or globally refer to notes by an alias. YAML is hidden in notes so you can add a lot of data to the YAML markup without making your notes messy.</p>
<p>YAML in obsidian typically looks like this:</p>
<p>```
---
alias: [how to use obsidian,obsidian guide]
tags: [note]
---
```</p>
<p>The dashes will go a different colour (By default, the dashes are green) if you have placed the YAML in your notes correctly.</p>
<p>By default obsidian supports the following YAML in this order:</p>
<p>1.  alias
2.  tags
3.  cssclass</p>
<p>You are able to add more YAML metadata but it’s not natively supported by obsidian. However, this can still be useful if you’re using plugins like _Dataview_</p>
<p>[Video] – Coming Soon</p>
<h2>Other Obsidian Content</h2>
<p>1.  <a href="https://rossgriffin.com/productivity/my-obsidian-personal-knowledge-management-system-walk-through/" class="external-link">My obsidian workflow walkthrough (30min video)</a></p>
<h3>_You may also be interested in:_</h3>
<p>[](https://rossgriffin.com/the-round-up/how-to-choose-your-priorities-like-warren-buffett/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0 "How to choose your priorities like Warren Buffett #23")</p>
<p>#### <a href="https://rossgriffin.com/the-round-up/how-to-choose-your-priorities-like-warren-buffett/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=0 "How to choose your priorities like Warren Buffett #23"" class="external-link">How to choose your priorities like Warren Buffett #23</a></p>
<p>Join The NewsletterThe following is an excerpt from The Round Up, a somewhat frequent newsletter that I send out. SubscribeI won\'t send you spam. Unsubscribe at any time.Built with ConvertKit Hey everyone,Welcome to all the new faces around here! It\'s really encouraging to see this newsletter growing. One of the…</p>
<p>[](https://rossgriffin.com/the-round-up/the-round-up-24/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1 "The Round Up #24")</p>
<p>#### <a href="https://rossgriffin.com/the-round-up/the-round-up-24/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=1 "The Round Up #24"" class="external-link">The Round Up #24</a></p>
<p>Join The NewsletterThe following is an excerpt from The Round Up, a somewhat frequent newsletter that I send out. SubscribeI won\'t send you spam. Unsubscribe at any time.Built with ConvertKit Hey everyone, As usual, welcome to all the new faces around here. Since I posted my obsidian markdown guide there…</p>
<p>[](https://rossgriffin.com/the-round-up/the-round-up-25/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2 "The Round Up #25")</p>
<p>#### <a href="https://rossgriffin.com/the-round-up/the-round-up-25/?relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2&relatedposts_hit=1&relatedposts_origin=1295&relatedposts_position=2 "The Round Up #25"" class="external-link">The Round Up #25</a></p>
<p>Hey Everyone, as usual welcome to all the new faces around here. Since I\'ve published my guide to Obsidian.md markdown I\'ve seen 10% growth in the Round Up Subscribers week-to-week. The amount of visitors my website gets has also increased by 100% since I published that silly guide. If you\'re…</p>
<p><a href="https://rossgriffin.com/the-round-up/how-to-quickly-create-new-habits-or-break-old-ones/" class="external-link">PrevPREVIOUSHow to quickly create new habits (or break old ones)</a></p>
<p><a href="https://rossgriffin.com/the-round-up/how-to-find-your-passion/" class="external-link">NEXTHow to find your passionNext</a></p>
<h3>Table of Contents</h3>
<p>-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#basic-text-formatting" class="external-link">Basic Text formatting</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#headings-and-horizontal-rules" class="external-link">Headings and Horizontal Rules</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#lists-and-checklists" class="external-link">Lists and Checklists</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#code-blocks" class="external-link">Code Blocks</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#tables" class="external-link">Tables</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#footnotes" class="external-link">Footnotes</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#linking" class="external-link">Linking</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#embedding-content" class="external-link">Embedding content</a>
    
    -   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#embedding-images-and-other-file-types" class="external-link">Embedding images and other file types.</a>
        
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#queries-and-search" class="external-link">Queries and Search</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#adding-meta-data" class="external-link">Adding Meta Data</a>
    
-   <a href="https://rossgriffin.com/tutorials/obsidian-basics-guide/#other-obsidian-content" class="external-link">Other Obsidian Content</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>