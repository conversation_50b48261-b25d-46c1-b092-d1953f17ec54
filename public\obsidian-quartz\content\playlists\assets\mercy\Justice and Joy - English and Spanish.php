<?php
// Auto-generated blog post
// Source: content\playlists\assets\mercy\Justice and Joy - English and Spanish.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'For everyone born, a place at the table,';
$meta_description = 'For everyone born, a place at the table, for everyone born, clean water and bread, a shelter, a space, a safe place for growing, for everyone born, a ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'For everyone born, a place at the table,',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'For everyone born, a place at the table, for everyone born, clean water and bread, a shelter, a space, a safe place for growing, for everyone born, a ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\mercy\\Justice and Joy - English and Spanish.md',
);

// Post content
$post_content = '<p>For everyone born, a place at the table,
for everyone born, clean water and bread,
a shelter, a space, a safe place for growing,
for everyone born, a star overhead.</p>
<p>Para todo nacidos, un lugar en la mesa,
para todo nacidos, agua limpia y pan,
un refugio, un espacio, un lugar segur crecer,
para todo nacido, estrella en alto.</p>
<p>[Coro:]</p>
<p>And God will delight when we are creators
of justice and joy, compassion and peace:
yes, God will delight when we are creators
of justice, justice and joy!</p>
<p>Y Dios se deleitará cuando seamos creadores.
de justicia y alegría, compasión y paz:
sí, Dios se deleitará cuando seamos creadores
de justicia, justicia y alegría!</p>
<p>For woman and man, a place at the table —
and all those between, beyond, and besides;
expanding our world, dismantling power,
each valued for what their voice can provide.</p>
<p>Para la mujer y el hombre, un lugar en la mesa.
y todos aquellos entre, más allá y más allá;
expandiendo nuestro mundo, desmantelando el poder,
cada uno valorado por lo que su voz puede ofrecer.</p>
<p>[coro]</p>
<p>For just and unjust, a place at the table,
a chance to repent, reform, and rebuild,
protecting the wronged, without shame or pressure,
for just and unjust, God’s vision fulfilled.</p>
<p>Para justos e injustos, un lugar en la mesa,
una oportunidad para arrepentirse, reformarse y reconstruirse,
protegiendo a los agraviados, sin vergüenza ni presión,
para justos e injustos, la visión de Dios cumplida.</p>
<p>[coro]</p>
<p>For gay, bi, and straight, a place at the table,
a covenant shared, a welcoming space,
a rainbow of race and gender and color
for queer, trans, and ace, the chalice of grace.</p>
<p>homo, bisexual y trangenero, un lugar en la mesa,
una alianza compartida, un espacio acogedor,
un arco iris de raza, género y color
para queer, trans y ace, el cáliz de la gracia.</p>
<p>[coro]</p>
<p>For sighted and blind, a place at the table,
for hearing and Deaf, all brain types and speech,
accessible space, rethinking our language,
all eager to learn from those who would teach.</p>
<p>Para videntes y ciegos, un lugar en la mesa,
para oyentes y sordos, todo tipo de cerebro y habla,
espacio accesible, repensar nuestro lenguaje,
todos ansiosos por aprender de aquellos que enseñarían.</p>
<p>[coro]
For everyone born, a place at the table
to live without fear, and simply to be,
to work, to speak out, to witness and worship,
for everyone born, the right to be free.</p>
<p>Para todo aquel que nace, un lugar en la mesa.
vivir sin miedo, y simplemente ser,
trabajar, hablar, testificar y adorar,
para todo aquel que nace, el derecho a ser libre.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>