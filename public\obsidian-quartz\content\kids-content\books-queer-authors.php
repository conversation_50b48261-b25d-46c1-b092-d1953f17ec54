<?php
// Auto-generated blog post
// Source: content\kids-content\books-queer-authors.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'By Lindz Amer @lindzamer |LISTEN TO RAINBOWS';
$meta_description = 'By Lindz Amer @lindzamer |LISTEN TO RAINBOWS \'I bet most of the people flipping out about drag queens and kids have no idea that their favorite classi...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'By Lindz Amer @lindzamer |LISTEN TO RAINBOWS',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'By Lindz Amer @lindzamer |LISTEN TO RAINBOWS \'I bet most of the people flipping out about drag queens and kids have no idea that their favorite classi...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kids-content\\books-queer-authors.md',
);

// Post content
$post_content = '<p>By Lindz Amer @lindzamer |LISTEN TO RAINBOWS</p>
<p>\'I bet most of the people flipping out
about drag queens and kids have no
idea that their favorite classic
children\'s books were written by
queer people</p>
<p>Arnold Lobel, the author of Frog and
Toad, came out to his family in the
mid-70s</p>
<p>Maurice Sendak lived with his male partner for 50 years (Where the wild things are)</p>
<p>Margaret Wise Brown was an iconic
chaotic bisexual. The "personal life and
death" section of her Wikipedia page is a
wild ride, I\'ve wanted to write a biopic
screenplay about her for ~ages~
(Goodnight Moon)</p>
<p>James Marshall was queer and died of Aids (George and Martha)</p>
<p>Tomie dePaola was gay and came out
later in life. The NYT gave him a beautiful
obituary after his death in 2020
(Strega Nona)</p>
<p>\'</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>