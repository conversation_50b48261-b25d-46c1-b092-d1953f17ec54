<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Cantemos Playlist.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Fetching Dataocj6https://open.spotify.com/track/3bl7sOQP1EsT6NTwWst5SA?si=6CMNkvSxTpK2cGmGC3PtoA&fbclid=IwZXh0bgNhZW0CMTAAAR3budWpE2fldHTw2o2Kww0vbxIT...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Fetching Dataocj6https://open.spotify.com/track/3bl7sOQP1EsT6NTwWst5SA?si=6CMNkvSxTpK2cGmGC3PtoA&fbclid=IwZXh0bgNhZW0CMTAAAR3budWpE2fldHTw2o2Kww0vbxIT...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Cantemos Playlist.md',
);

// Post content
$post_content = '<p><a href="https://open.spotify.com/track/3bl7sOQP1EsT6NTwWst5SA?si=6CMNkvSxTpK2cGmGC3PtoA&fbclid=IwZXh0bgNhZW0CMTAAAR3_budWpE2fldHTw2o2K_ww0vbxITu78_ATSKCdhQIqpxmurEMvP9v36CA_aem_k8Ojr8L0KwJMwrXOiKSKuQ&nd=1&dlsi=e677685ebdbf4edb" class="external-link">Fetching Data#ocj6</a><a href="https://open.spotify.com/track/3bl7sOQP1EsT6NTwWst5SA?si=6CMNkvSxTpK2cGmGC3PtoA&fbclid=IwZXh0bgNhZW0CMTAAAR3_budWpE2fldHTw2o2K_ww0vbxITu78_ATSKCdhQIqpxmurEMvP9v36CA_aem_k8Ojr8L0KwJMwrXOiKSKuQ&nd=1&dlsi=e677685ebdbf4edb" class="external-link">Cantemos - song by Héctor Lavoe, Willie Colón, Yomo Toro | Spotify</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>