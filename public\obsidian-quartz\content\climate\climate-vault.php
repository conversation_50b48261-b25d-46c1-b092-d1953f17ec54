<?php
// Auto-generated blog post
// Source: content\climate\climate-vault.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'In September of 2015 I participated in a hunger strike to deliver a copy of Pope Francis\' Encyclical to some of the worst polluters on the planet. Pop...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'In September of 2015 I participated in a hunger strike to deliver a copy of Pope Francis\' Encyclical to some of the worst polluters on the planet. Pop...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\climate-vault.md',
);

// Post content
$post_content = '<p>In September of 2015 I participated in a hunger strike to deliver a copy of Pope Francis\' Encyclical to some of the worst polluters on the planet. [[Pope Francis Climate Points]]</p>
<p>[[Eco-Sattva Vows]]</p>
<p>As a person who works out of the kitchen, I find ways to bring environmental stewardship to our food system. [[Bringing Eco-Stewardship into the Kitchen]]
[[climate-vault]]
![[4thofjulyculbertsonnebraska.jpg]]
![[bestclimatedoc(1).jpg]]
![[cactus.png]]
![[Christmas Card(1).jpg]]
![[Zuni News Article one page.pdf]]
![[cmarch.png]]
![[desert.jpg]]
![[desertsign.png]]
![[DirtRoad.jpg]]
![[Double Rainbow.jpg]]
![[dupagenews.png]]
![[earlymarchersworm.jpg]]
[[Eco-Sattva Vows]]
![[ecocommodebreak.jpg]]
![[hoodwinked.jpg]]
![[Keep Marching!.pdf]]
![[PDiGiacomo_SP21_UH3614-Final-Infographic-Chestnuts.pdf]]
[[Pope Francis Climate Points]]
![[popeFrancisClimatePoints.jpg]]
![[Rainy Road.jpg]]
![[spellsfortoughtimes_kerriconnor_environment.pdf]]
[[Tending the Soil - Lessons for Organizing]]
![[Tumbleweed.jpg]]
![[walkinghere.jpg]]
![[walkingheredesert.jpg]]
![[Wecantlivehereanymore.png]]
![[windcommode.png]]</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>