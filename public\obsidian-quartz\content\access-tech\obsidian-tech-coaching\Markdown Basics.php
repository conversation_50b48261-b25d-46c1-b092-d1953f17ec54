<?php
// Auto-generated blog post
// Source: content\access-tech\obsidian-tech-coaching\Markdown Basics.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'CompassionateCities';
$meta_description = '6 Most Important Keys to Typing in Markdown The Link cms/blogposts/Othering  by Maude The Tag CompassionateCities  Italics. emphasize Bold Big Lists. ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'CompassionateCities',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => '6 Most Important Keys to Typing in Markdown The Link cms/blogposts/Othering  by Maude The Tag CompassionateCities  Italics. emphasize Bold Big Lists. ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\obsidian-tech-coaching\\Markdown Basics.md',
);

// Post content
$post_content = '<p>6 Most Important Keys to Typing in Markdown</p>
<p>The Link [[cms/blogposts/Othering  by Maude]]</p>
<p>The Tag
#CompassionateCities</p>
<p>Italics. <em>emphasize</em></p>
<p>Bold
<strong>Big</strong></p>
<p>Lists.</p>
<p>-  Dash In front</p>
<p>1. Numbered
2. 2
3.</p>
<p>Headers
<h3>= H3</h3></p>
<p>~~--strikethrough--</p>
<p>==highlight==</p>
<h2>Hotkeys</h2>
<p>Open link in edit mode (control click on link)</p>
<p>toggle edit/preview (control e)</p>
<p>Open Quick Switcher (control o)</p>
<p>Search (control shift f)</p>
<p>Back (control alt left arrow)</p>
<p>Forward (control alt right arrow)</p>
<p>New note (control n)</p>
<p>Open note in new window 
(control shift n)</p>
<h2>Block References!</h2>
> "Quote" 
> > - Me 
^123
![[Note Name^1]]
<p>Gall\'s Law states all complex systems that worked evolved froms impler systems that worked. If you want to build a complex system that works, build a simpler system first, and then improve it over time.</p>
<p>8 Most Important Settings in Obsidian</p>
<p>Editor
Spell Check</p>
<p>Plug in
Tag Pane, Page Preview, Starred</p>
<p>File
Deleted Files, <strong>Always Update Internal Links</strong></p>
<p>Appearance
Custom CSS</p>
<p>Keep Deleted files in System Trash</p>
<p>Export PDF Option</p>
<p>Learn Footnotes Feature</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/1989de7d-03f4-4240-a49a-f8b9f7f1defe.png)</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/7d38e179-0e9d-4942-828b-c3ab67d054fd.png)</p>
<p>Gatsby - Static Site Generator</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/26a61836-aab7-42b2-a73d-2204d30fa75c.png)</p>
<p>There\'s PHP Markdown?? Tables?? Task Lists??</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/a945bc5c-549e-4382-8d71-c99e8bd82149.png)</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/05c362b7-0abd-4f90-9b31-cf35e0091476.png)</p>
<p>\\ \\ to negate effect</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/44423d85-7599-405a-b1ad-acda918969af.png)</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/3a98f686-524b-490e-bbb8-3002ae3ca0ff.png)</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/b9405e8e-cb97-4159-9ac9-8c8ae131a902.png)</p>
<p>![](https://slid-users-assets-v1-ohio.s3.us-east-2.amazonaws.com/public/capture_images/b3aafbe43c0547eaaeb38da7669d8fe6/40b596a6-11bb-4708-90c4-b128bcfb9c79.png)</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>