<?php
// Auto-generated blog post
// Source: content\playlists\assets\misc\e blues scale.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'The notes you\'ve listed E, F, F, A, Bb, B can be found in the E Blues Scale. The E Blues Scale is a six-note scale that includes the following notes: ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'The notes you\'ve listed E, F, F, A, Bb, B can be found in the E Blues Scale. The E Blues Scale is a six-note scale that includes the following notes: ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\misc\\e blues scale.md',
);

// Post content
$post_content = '<p>The notes you\'ve listed (E, F, F#, A, Bb, B) can be found in the E Blues Scale. The E Blues Scale is a six-note scale that includes the following notes: E, G, A, Bb, B, and D.</p>
<p>The E Blues Scale is derived from the major pentatonic scale with an added b3 (flat third) note. In this case, the major pentatonic scale for E is: E, F#, G#, A, B, and D. The blues scale adds the b3 (G# becomes G), so it becomes E, G, A, Bb, B, and D.</p>
<p>In your minor riff, you have E, F, F#, A, Bb, and B. This is essentially the E Blues Scale with an added F# (which is not part of the standard E Blues Scale but is still musically usable).</p>
<p>While the E Blues Scale might be the closest fit, it\'s worth mentioning that in the blues, you can use "blue notes" which are notes that are slightly altered from the standard major or minor scales. These notes are usually between the major and minor third and between the perfect fourth and the augmented fourth. In the context of your riff, the F and F# could be considered blue notes.</p>
<p>So, while there isn\'t an exact blues scale or harmonic scale that includes all of the notes in your riff, the E Blues Scale is a close match, and the use of blue notes can account for the F and F#.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>