<?php
// Auto-generated blog post
// Source: content\climate\encyclical-environment.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Pope Francis Climate Talking Points';
$meta_description = 'I went six days without eating. In the days prior to the visit, I was biking in the city, and ran into somebody who saw my t-shirt and offered two tickets to see the Pope. Here are some boiled down ideas from their Encyclical on the Environment we hunger striked over.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Pope Francis Climate Talking Points',
  'author' => 'A. A. Chips',
  'date' => '2015-09-28',
  'excerpt' => 'I went six days without eating. In the days prior to the visit, I was biking in the city, and ran into somebody who saw my t-shirt and offered two tickets to see the Pope. Here are some boiled down ideas from their Encyclical on the Environment we hunger striked over.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\encyclical-environment.md',
);

// Post content
$post_content = '<p>On September 24th, 2015, I got to see Pope Francis speak on their Encyclical on the Environment in Washington D.C. I was part of a group that in the eighteen days before, was <a href="https://archive.org/details/FERCFastBrokenAndMarch9252015540p" target="_blank">engaged in a hunger strike</a>. I went six days without eating. In the days prior to the visit, I was biking in the city, and ran into somebody who saw my t-shirt and offered two tickets to see the Pope. They had been given those tickets by their work with the Energy Department, and couldn\'t make it. The lines were opening at 6:00 AM and the night before, many of us camped outside by the Meade Memorial, staying up all night in the cold singing songs and drinking tea. By the time we were lining up, even with that early set arrival, Pope Francis was a dot on the horizon when they gave their speech.</p>
<p>This visit to the United States was to three cities, New York, Washington DC, and Chicago. This was to deliver their message, their encyclical on the environment. <a href="https://www.vatican.va/content/francesco/en/encyclicals/documents/papa-francesco_20150524_enciclica-laudato-si.html" target="_blank">Laudato Si</a>, or \'On Care for our Common Home,\' criticizes consumerism, irresponsible economic development, environmental degradation, and global warming. This meme boils down the document into seven talking points.</p>
<p><img src="../../img/edutainment/popeFrancisClimatePoints.jpg" width="400" alt="Pope Francis Climate Talking Points."></p>
<p><em>Any harm done to the environment is harm done to humanity. The climate is a common good, belonging to all, and meant for all.</em></p>
<p><em>We are not faced with two separate crises, one environmental and the other social. But one crisis which is both social and environmental. The poor and earth are crying out</em></p>
<p><em>What is at stake is our own dignity. Climate change dramatically affects us, for it has to do with the ultimate meaning of our earthly sojourn.</em></p>
<p><em>We are one single human family. We have a shared responsibility for our common home. What kind of world do we want to leave to children who are now growing up?</em></p>
<p><em>Now is the time for an integrated approach to combating poverty, restoring dignity to the excluded, and at the same time protecting nature.</em></p>
<p><em>There is an urgent need for policies so that drastically reduce coal, oil, and gas, and replace them with renewable energy.</em></p>
<p><em>I am convinced we can make a difference. I am confident we can find a solution.</em></p>
<p><em>I call for a courageous effort to redirect our steps.</em></p>
<p>https://www.vaticannews.va/en/pope/news/2023-10/laudate-deum-pope-francis-climate-crisis-laudato-si.html</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>