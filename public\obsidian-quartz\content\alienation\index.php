<?php
// Auto-generated blog post
// Source: content\alienation\index.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Family Alienation and Recovery Content';
$meta_description = 'Family alienation and recovery content.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Family Alienation and Recovery Content',
  'author' => 'A. A. Chips',
  'date' => '2025-10-04',
  'excerpt' => 'Family alienation and recovery content.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\alienation\\index.md',
);

// Posts for this category
$content_posts = [];

// All available posts data - this would normally be populated by build.php
// For now, let's scan the alienation directory for posts
$alienationDir = __DIR__;
$posts = glob($alienationDir . '/*.php');
foreach ($posts as $postFile) {
    $filename = basename($postFile, '.php');
    if ($filename !== 'index' && $filename !== 'contents' && $filename !== 'notes') {
        // Create a basic post entry
        $content_posts[] = [
            'title' => ucwords(str_replace('-', ' ', $filename)),
            'url' => $filename . '.php',
            'excerpt' => 'Alienation and recovery content.',
            'date' => date('Y-m-d'),
            'author' => 'A. A. Chips',
            'thumbnail' => null
        ];
    }
}

// Debug: Let's see what files we found
echo "<!-- Debug: Found " . count($content_posts) . " posts in alienation directory -->";
echo "<!-- Debug: Directory: " . $alienationDir . " -->";
echo "<!-- Debug: Glob pattern: " . $alienationDir . '/*.php' . " -->";
echo "<!-- Debug: Files found: " . implode(', ', array_map('basename', $posts)) . " -->";

// Post content
$post_content = '<p>I\'m an alienated family member whose actually wonderful.</p>
<p>If you need someone supportive in your life. Who believes in you, and will deliver helpful content. Look no further and subscribe. And maybe comment some questions, video ideas, or a bit about yourself.</p>
<p>I care about your feedback and ideas. Let me know what you would like to see.</p>
<p>if you\'ve heard rumors about me, they are probably true.  Even if it\'s not.</p>
<p>Help out the algorithm by hitting the right buttons. I got to find someone on here.</p>
<p>I hope you have a great day. If you haven\'t, go and drink some water.</p>
<p>I put together this vault of information about Parental and Family Alienation. As helpful as it can be, it is also distressing information to swallow.</p>
<p>I\'m putting this page together to start a conversation on a topic that is very difficult. I consider this to be a generational curse in my family of upbringing. When I talk about Alienation, it can mean a lot of different things to different people.</p>
<p><img src="../../img/art/painFamilies.jpg" alt="Pain travels through families until somebody is ready to feel it. Quote by Stephi Wagner." width="400"></p>
<p>To me, what Alienation is is the constant need for there to be an enemy, or someone to throw under the bus. I witnessed this behavior my entire life and didn\'t speak on it, until I was on the receiving end. This happens through manipulation, conveying of exaggerated or false information, and attacks on one\'s character. It is generally done out of extreme fear and insecurity, but can be done maliciously. Kids get put in the middle of grown up conflicts, and everyone ends up losing.</p>
<p>It is a form of psychological abuse and violence. There is settled research on this phenomenon, and lots of pseudoscience and crazy people who speak on it as well. There are people who dispute the existence of Parental and Family Alienation, and generally these are not good faith arguments, but ones of financial conflicts of interest. This comes up during high conflict divorces, separations, and estrangements. It is not relevant to everyone\'s situation. But it\'s absolutely relevant to mine.</p>
<p>I am not putting this together to argue a case, or argue that I am a good person, or attack anyone. If the shoe fits, it fits, however.</p>
<p>I\'m putting this together because for the past eight years, I haven\'t really been allowed to share my point of view around the people who are doing this. It has impacted me heavily and inflicted a heartache and dread I wouldn\'t wish on my worst enemy, let alone the kids who are burdened to carry the weight of this their entire life.</p>
<p>I\'m sharing this because the young people caught as child soldiers in a divorce and estrangement war deserve to hear the other side, even if it is painful. It is also freeing and liberating. I love you and I\'m sorry you were put in the middle of this.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->

<?php if (!empty($content_posts)): ?>
<div class="post-grid">
    <?php foreach ($content_posts as $post_item): ?>
        <div class="post-card">
            <a href="<?php echo htmlspecialchars($post_item['url'] ?? '#'); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post_item['thumbnail']) && $post_item['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post_item['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post_item['thumbnail']); ?>"
                                 alt="<?php echo htmlspecialchars($post_item['title'] ?? ''); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post_item['thumbnail']); ?>"
                                 alt="<?php echo htmlspecialchars($post_item['title'] ?? ''); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>"
                             alt="<?php echo htmlspecialchars($post_item['title'] ?? ''); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post_item['title'] ?? ''); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post_item['excerpt'] ?? '') ? $post_item['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post_item['author']) && $post_item['author'] && is_string($post_item['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post_item['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post_item['date']) && $post_item['date']): ?>
                            <span class="post-date"><?php echo is_string($post_item['date']) ? htmlspecialchars($post_item['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post_item['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post_item['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </a>
        </div>
    <?php endforeach; ?>
</div>
<?php else: ?>
    <p>No posts found in this category.</p>
<?php endif; ?>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>