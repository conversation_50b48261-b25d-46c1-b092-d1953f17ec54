<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Poem in Ladino.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Poem in Ladino';
$meta_description = 'Poem in Ladino Hint by April: x is a hard ésh. Think about vosotros tense of verbs having the \'aís\', \'eís,\' or \'ís\', but it is a harder sound, \'as...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Poem in Ladino',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Poem in Ladino Hint by April: x is a hard ésh. Think about vosotros tense of verbs having the \'aís\', \'eís,\' or \'ís\', but it is a harder sound, \'as...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Poem in Ladino.md',
);

// Post content
$post_content = '<h3>Poem in Ladino</h3>
*[Hint by April: x is a hard ésh. Think about vosotros tense of verbs having the \'aís\', \'eís,\' or \'ís\', but it is a harder sound, \'ash\', \'esh\', \'ish.\' Ladino uses much less diacritical marks than Spanish does. The formal you \'vos\' is both good in singular and plural.]
<p>Enpesare a kontar
Una valiente estoria
Ke olarex de atentar
Y tomarex grande gloria.</p>
<p>Loarex, bendizerex
Al Dio alto y enxalxado;
De este mundo savrex
Las cozas ke an pasado. ( you will know what happened in this world)</p>
<p>Seax guardados de estorvo
Kon toda vuestra famia,
Y nunka veax estorvo, 
Sino todo alegria.</p>
<p>Dichozos y prosperados
En bienes y rikezas (Blissful and prosperous, in goodness and riches and wealth..)
Sienpre seax enbidiados (may you always be envied)
Y avierta vuestra meza. (and your table remain open.)</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>