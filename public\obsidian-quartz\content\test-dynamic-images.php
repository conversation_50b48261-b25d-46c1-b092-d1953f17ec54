<?php
// Auto-generated blog post
// Source: content\test-dynamic-images.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Testing Dynamic Image References';
$meta_description = 'Testing the new dynamic image reference system that pulls data from gallery.json';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Testing Dynamic Image References',
  'author' => 'A. A. Chips',
  'date' => '2025-01-08',
  'excerpt' => 'Testing the new dynamic image reference system that pulls data from gallery.json',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\test-dynamic-images.md',
);

// Post content
$post_content = '<h1>Testing Dynamic Image References</h1>
<p>This post tests the new dynamic image reference system. Instead of manually writing `<img>` tags with paths and alt text, I can now just reference images from my gallery using simple syntax.</p>
<h2>Self Images</h2>
<p>Here\'s me angling in a hammock:
<div class="dynamic-image-container"><img src="img/self/30degrees.png" alt="Angling in a hammock for optimal comfort." loading="lazy" width="1028" height="838"><div class="image-caption">Angling in a hammock for optimal comfort.</div><button class="toggle-commentary" onclick="toggleCommentary(\'commentary-695c90cd6bc1f1aed85f5a5b79429166\')">Show Commentary</button><div id="commentary-695c90cd6bc1f1aed85f5a5b79429166" class="image-commentary" style="display: none;"><strong>Commentary:</strong> Many people who try sleeping in a traditional hammock once will say they can\'t do it because of back or neck pain. In almost all cases, they are laying in the hammock straight, which is incorrect for comfort. Try angling yourself to about 30 degrees, or whatever is optimally comfortable.</div></div></p>
<p>And here\'s me proving I\'m not Mr. Smee:
<div class="dynamic-image-container"><img src="img/self/not-smee.png" alt="" loading="lazy" width="1204" height="1599"></div></p>
<h2>Humor Images</h2>
<p>A classic meme about social media validation:
<div class="dynamic-image-container"><img src="img/humor/22-likes.png" alt="meme text - I once posted a meme that got 22 likes so maybe you should think twice before you speak to me that way" loading="lazy" width="1080" height="1105"><div class="image-caption">meme text - I once posted a meme that got 22 likes so maybe you should think twice before you speak to me that way</div><button class="toggle-commentary" onclick="toggleCommentary(\'commentary-2bc6943f205e5ea1eee1893220f67f21\')">Show Commentary</button><div id="commentary-2bc6943f205e5ea1eee1893220f67f21" class="image-commentary" style="display: none;"><strong>Commentary:</strong> I don\'t go to Facebook anymore..</div></div></p>
<h2>Art Images</h2>
<p>Some thought-provoking art:
<div class="dynamic-image-container"><img src="img/art/MiniCarMiniProfit.png" alt="Image of three wheeled mini car with Henry Ford quote Mini cars drive a mini profit" loading="lazy" width="403" height="292"><div class="image-caption">Image of three wheeled mini car with Henry Ford quote Mini cars drive a mini profit</div><button class="toggle-commentary" onclick="toggleCommentary(\'commentary-17c305b9f84131409c9c12e2448c9957\')">Show Commentary</button><div id="commentary-17c305b9f84131409c9c12e2448c9957" class="image-commentary" style="display: none;"><strong>Commentary:</strong> We don\'t have better cars because better cars mean less money for car companies and industry as a whole.</div></div></p>
<h2>Educational Content</h2>
<p>Important information about trauma responses:
<span class=\'missing-image\'>[Image not found: traumamanifesto.jpg]</span></p>
<h2>Testing Different Reference Styles</h2>
<h3>By basename only</h3>
<div class="dynamic-image-container"><img src="img/self/30degrees.png" alt="Angling in a hammock for optimal comfort." loading="lazy" width="1028" height="838"><div class="image-caption">Angling in a hammock for optimal comfort.</div><button class="toggle-commentary" onclick="toggleCommentary(\'commentary-5c54628d797b21698362e7a779213d11\')">Show Commentary</button><div id="commentary-5c54628d797b21698362e7a779213d11" class="image-commentary" style="display: none;"><strong>Commentary:</strong> Many people who try sleeping in a traditional hammock once will say they can\'t do it because of back or neck pain. In almost all cases, they are laying in the hammock straight, which is incorrect for comfort. Try angling yourself to about 30 degrees, or whatever is optimally comfortable.</div></div>
<h3>By full path</h3>
<div class="dynamic-image-container"><img src="img/self/30degrees.png" alt="Angling in a hammock for optimal comfort." loading="lazy" width="1028" height="838"><div class="image-caption">Angling in a hammock for optimal comfort.</div><button class="toggle-commentary" onclick="toggleCommentary(\'commentary-e996d5964098709c8edb65c87f1c8dcc\')">Show Commentary</button><div id="commentary-e996d5964098709c8edb65c87f1c8dcc" class="image-commentary" style="display: none;"><strong>Commentary:</strong> Many people who try sleeping in a traditional hammock once will say they can\'t do it because of back or neck pain. In almost all cases, they are laying in the hammock straight, which is incorrect for comfort. Try angling yourself to about 30 degrees, or whatever is optimally comfortable.</div></div>
<h3>Non-existent image (should show error)</h3>
<span class=\'missing-image\'>[Image not found: this-does-not-exist.png]</span>
<h2>Benefits of This System</h2>
<p>1. <strong>Single source of truth</strong>: All image metadata lives in gallery.json
2. <strong>Automatic alt text</strong>: No more forgetting accessibility
3. <strong>Rich captions</strong>: Automatic captions from my curated descriptions
4. <strong>Commentary</strong>: Optional deeper context with toggle buttons
5. <strong>Consistent styling</strong>: All images follow the same design
6. <strong>No broken paths</strong>: No more `../../img/` path issues
7. <strong>Easy maintenance</strong>: Update alt text once, changes everywhere</p>
<p>This makes managing images across my blog so much easier!</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>