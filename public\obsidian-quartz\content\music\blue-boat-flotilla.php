<?php
// Auto-generated blog post
// Source: content\music\blue-boat-flotilla.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '(Verse 1)';
$meta_description = 'Verse 1 C           F           G   Though the waves are rising high,   C       Am      G       C Beneath a unforgiving sky,   C           F          ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '(Verse 1)',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Verse 1 C           F           G   Though the waves are rising high,   C       Am      G       C Beneath a unforgiving sky,   C           F          ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\music\\blue-boat-flotilla.md',
);

// Post content
$post_content = '<p>(Verse 1)
C           F           G  
Though the waves are rising high,  
C       Am      G       C
Beneath a unforgiving sky,  
C           F               G
Our hull is filled with simple bread,
C       Am      G       C     
And hopes for mouths left underfed. 
Em      Am7       Dm7       G7 
Stars above, our only chart,  
Em      Fm7     Dm7     G7
We sail with a determined heart,
C           F           C       G         
Through the silence and the dread,
C       F       C       G           c  
Towards the shores where hope has fled.</p>
<p>(Chorus)  
Sail, sail our weary boat,  
On a sea where fear does float.  
Carry life past the blockade\'s line,  
Oh, suffering people, yours and mine.  
Through the gunships and the glare,  
On a sea of deep despair,  
Guide her through the deadly night,  
A fragile, persevering light.</p>
<p>(Verse 2)  
See the children on the sand,  
In a bombed and broken land.  
Their empty hands reach for the shore,  
Where aid is stopped by war and law.  
A mighty wall of steel and fear,  
Holds back the help, we hold so dear.  
But still we turn our battered prow,  
We make and keep this promise now.</p>
<p>(Chorus)  
Sail, sail our weary boat,  
On a sea where fear does float.  
Carry life past the blockade\'s line,  
Oh, suffering people, yours and mine.  
Through the gunships and the glare,  
On a sea of deep despair,  
Guide her through the deadly night,  
A fragile, persevering light.</p>
<p>(Bridge)  
And the world just watches on,  
As another day is gone.  
But this vessel, small and slow,  
Says a truth it cannot hide:  
No child should have to starve inside,  
A world that has so much to give,  
A world where we are meant to live.</p>
<p>(Chorus)  
Sail, sail our weary boat,  
On a sea where fear does float.  
Carry life past the blockade\'s line,  
Oh, suffering people, yours and mine.  
Through the gunships and the glare,  
On a sea of deep despair,  
Guide her through the deadly night,  
A fragile, persevering light.</p>
<p>(Outro)  
Oh, Gaza, waiting there,  
In the thin and wounded air...  
We are coming, hold on fast...  
This indignity will not last.  
Though we are just a wooden wall,  
We will not let your children fall.  
We are coming, hold on fast...  
This indignity will not last.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>