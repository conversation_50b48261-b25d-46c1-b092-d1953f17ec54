<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Steven top 10.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Love like you (Credit Theme)';
$meta_description = 'I\'m looking at inspiration in that cartoon I mentioned on one of the first assignments. Steven Universe is vector illustration brought to life. It\'s a...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Love like you (Credit Theme)',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'I\'m looking at inspiration in that cartoon I mentioned on one of the first assignments. Steven Universe is vector illustration brought to life. It\'s a...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Steven top 10.md',
);

// Post content
$post_content = '<p>I\'m looking at inspiration in that cartoon I mentioned on one of the first assignments. Steven Universe is vector illustration brought to life. It\'s a kids show written for grown ups and deals with themes of unthinkable loss. The soundtrack in the show is fantastic. Some of the songs are incredibly cheerful, some paint incredible sadness, and many emotions in between. It\'s a great wholesome cartoon that I\'d recommend. All of the soundtrack is free on Youtube. Here\'s ten songs with links you and or Laura might like from the show.</p>
<h3>Love like you (Credit Theme)</h3>
<a href="https://www.youtube.com/watch?v=7_ej9JYZJf0&list=PLg6KfZlgBuDXBkvgBrU9_jaXkl25VzfPm&index=44" class="external-link">Steven Universe | Rebecca Performs "Love Like You" ft. Aivi & Surasshu | Cartoon Network - YouTube</a>
<h3>Do it for Her </h3>
<a href="https://www.youtube.com/watch?v=bfszKWl7GMo&list=PLg6KfZlgBuDXBkvgBrU9_jaXkl25VzfPm&index=14" class="external-link">“Do It For Her" | Steven Universe | Cartoon Network - YouTube</a>
<h3>No Matter What</h3>
<a href="https://www.youtube.com/watch?v=O9_wACQvhys&list=PLg6KfZlgBuDXBkvgBrU9_jaXkl25VzfPm&index=23" class="external-link">Steven Universe The Movie - No Matter What - (OFFICIAL VIDEO) - YouTube</a>
<h3>Just a Comet</h3>
<a href="https://www.youtube.com/watch?v=t3M47LlGAWU" class="external-link">Steven Universe I "Just a Comet" I Cartoon Network - YouTube</a>
performed during
<a href="https://www.youtube.com/watch?v=Oepl_hkgfTY" class="external-link">Steven Universe | Greg Meets Rose for the First Time! | Cartoon Network - YouTube</a>
<h3>True Kinda Love</h3>
<a href="https://www.youtube.com/watch?v=esh3q3aaP7Y" class="external-link">Steven Universe: The Movie | True Kinda Love - Singalong | Cartoon Network UK 🇬🇧 - YouTube</a>
<h3>Cartoon Short-Extended Intro Theme</h3>
https://www.youtube.com/watch?v=e9-_9eBRaZo&list=PLg6KfZlgBuDXBkvgBrU9_jaXkl25VzfPm&index=96
<h3>Being Human</h3>
<a href="https://www.youtube.com/watch?v=INKqi2Tihi8&list=PLg6KfZlgBuDXBkvgBrU9_jaXkl25VzfPm&index=123" class="external-link">Steven Universe Future | Being Human - YouTube</a>
<h3>When Greg Met Rose</h3>
<a href="https://www.youtube.com/watch?v=24lI7EYYbRw" class="external-link">Steven Universe | When Greg Met Rose | Cartoon Network - YouTube</a>
<h3>Here Comes a Thought</h3>
This song is it\'s own mental health PSA for young people and grown-ups alike. "Take a moment and just think of just flexibility, love, and trust." Estelle, who wrote the song, and plays the voice of Garnet, has several Black-positive iconic hit wonder songs on the show, including \'True Kinda Love.\' Garnet is a fusion / cross-between the characters Sapphire and Ruby.
<a href="https://www.youtube.com/watch?v=dHg50mdODFM" class="external-link">"Here Comes a Thought" | Steven Universe | Cartoon Network - YouTube</a>
<h3>Both of You </h3>
I have a show relevant theory related specifically to this song and the animation. Much of the show as Steven is young, he is obsessed with figuring out what his powers are that he got from his Mom, Rose Quartz, who was a gem alien from across the universe thousands of years old. He develops a lot of powers throughout the show as he gets older, but one that is never discussed I think is the power of music. Rose Quartz had a thing for musical human men, for possibly thousands of years, and probably learned everything there is to learn about music by watching admiringly many human lifetimes of music playing men, and never touching in instrument. Pearl has a \'Rocket Science\' grasp of engineering, despite Pearls being made on the Homeworld Planet being designed to serve their master and look presentable, which was probably learned in the same way. 
And that this \'power of music\' is from it\'s ability to heal others. Steven goes from learning powers that are super cool in battle, to developing ones based on healing others suffering, and having the courage to have difficult conversations. All of this is communicated with animation and vector art.   
<a href="https://www.youtube.com/watch?v=lfFKgsyPNaw" class="external-link">Steven Universe | Both of You | Cartoon Network - YouTube</a>
<p>For Just One Day Let\'s Only Think About (Love)</p>
<p>Isn\'t it Love</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>