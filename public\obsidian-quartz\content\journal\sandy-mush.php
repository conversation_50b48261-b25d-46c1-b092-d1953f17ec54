<?php
// Auto-generated blog post
// Source: content\journal\sandy-mush.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Sandy Mush';
$meta_description = 'Sandy Mush - A. A. Chips';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Sandy Mush',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'excerpt' => 'Sandy Mush - A. A. Chips',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\sandy-mush.md',
);

// Post content
$post_content = '<h3><strong>THE MARYELLA CHRONICLES: SANDY MUSH</strong></h3>
<p>Laura’s land was beautiful in the way a wound is beautiful when it starts to heal—knotted with scars, but still alive. Three acres of tangled North Carolina hardwoods, a creek choked with the ghosts of old comforters, and a trailer that sagged under the weight of its own dread. The neighbors called it "that trashy parcel," but Laura knew better. She’d point to the perennials pushing through the wreckage—echinacea, yarrow, the stubborn Cherokee roses—and say, _"See? The land remembers what to do even when people don’t."_</p>
<p>We were there because Maryella had struck a bargain with her: _We clean, we camp, we help with taxes._ A fair deal, if you ignored the kerosene-stained trailer where Laura’s son Tche sometimes slept like a man trying to prove he was already dead. The first time I stood near it, the air went thick. Not the damp-rot stench of moldering upholstery, but something older. A house had burned here decades ago. Something violent had happened. The land whispered it.</p>
<p>Maryella didn’t need whispers. She was a witch, or close enough—the kind who could "landscape" a hillside into submission without a single weed-whacker. _"Signs are for people who don’t know how to listen,"_ she’d say, while her kids scrambled through the ferns like wild things. Homeschooled in the woods, fed on foraged greens and whatever the church food pantry donated that week. The neighbors hated us. Not just for Tche’s larcenous habits, or the smoke when we burned what couldn’t be saved, but for the way we lived: half-feral, half-holy, a spectacle of poverty they couldn’t look away from.</p>
<p>The worst was the dog. A rangy mutt that adopted us, tore holes in tents, and led us straight to the property line where a man in a camo hat hissed, _"Next time, I shoot."_ His kids watched from the porch, wide-eyed. Maryella just laughed later, sharp as a knife. _"They’re scared we’ll teach the land to talk back."_</p>
<p>But Laura? Laura was dying. Pulmonary disease gnawed at her, but she’d still limp down to the creek with us, watching as we hauled waterlogged comforters from the muck. Seventy pounds of fabric, dead weight, stinking of river and time. _"This was somebody’s bed once,"_ she’d murmur. _"Now it’s just another thing on fire."_</p>
<p>And Tche? He’d stand too close to the flames, breathing in the fumes like they could fill whatever hole he’d been born with.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>