<?php
// Auto-generated blog post
// Source: content\climate\climacool.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Melting every time you step outside?';
$meta_description = 'Melting every time you step outside? Dreading another summer of sun-scorched sidewalks and air thick enough to chew?  Introducing the ClimaCool Oasis ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Melting every time you step outside?',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Melting every time you step outside? Dreading another summer of sun-scorched sidewalks and air thick enough to chew?  Introducing the ClimaCool Oasis ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\climacool.md',
);

// Post content
$post_content = '<h2>Melting every time you step outside?</h2>
<p>Dreading another summer of sun-scorched sidewalks and air thick enough to chew?</p>
<h1>Introducing the ClimaCool Oasis 3000!</h1>
<p>Say goodbye to flimsy foliage! Our precision engineered, weather-resistant canopy blocks the sun’s wrath with military-grade intensity.</p>
<p>!<a href="https://aachips.co/wp-content/uploads/Untitled-1-1-768x768.png" class="external-link">Climacool 3000 AI generated bad engineered version of tree cell phone tower with wig.</a></p>
<h2>Features</h2>
<h3>Sun Protection</h3>
<p>Choose from sleek metallic or vibrant holographic finishes, guaranteed to turn heads (and confuse pigeons)</p>
<h3>Air Conditioning</h3>
<p>Our cutting-edge, whisper-quiet (okay, maybe a slight hum) air conditioning system pumps out an arctic blast that rivals a penguins freezer.</p>
<h3>Self-Replenishing Reservoir</h3>
<p>A self-replenishing reservoir collects precious dew (or, you know, rain) for instant refreshment. </p>
<h3>LED Lighting System</h3>
<p>Energy-efficient (read: nuclear-powered, but don’t worry, it’s totally safe…ish) LED lighting system banishes the night with the brilliance of a thousand suns.</p>
<h2>Benefits</h2>
<h3>Environmentally Friendly</h3>
<p>As environmentally friendly as a miniature nuclear reactor can be.</p>
<h3>Conversation Starter</h3>
<p>Guaranteed to be the talk of the town! Or at least spark some lively debates about alternative energy.</p>
<h2>Ready to experience the future?</h2>
<p>Order Yours Today</p>
<p>We’re not responsible for any spontaneous meltdowns, rogue AI uprisings, or unexplained glowing pigeons caused by the ClimaCool Oasis 3000. Use at your own risk (and with a healthy dose of skepticism).</p>
<h3>Seriously. Have you considered planting a tree?</h3>
<p>They’re pretty cool, nature’s original air conditioners and all.</p>
<p>Donate to Asheville Tree Conservancy<a href="https://aachips.co/climacool/" class="external-link">Learn more about what Trees do here..</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>