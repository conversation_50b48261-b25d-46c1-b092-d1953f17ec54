<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Mulan.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'E/D# C#m7 C#m/B A B E';
$meta_description = 'Instrumental E/D Cm7 Cm/B A B E Verse 1 Em          D/F         G Let\'s get down to business D/F G      Am          D To defeat the Huns Em          D...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'E/D# C#m7 C#m/B A B E',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Instrumental E/D Cm7 Cm/B A B E Verse 1 Em          D/F         G Let\'s get down to business D/F G      Am          D To defeat the Huns Em          D...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Mulan.md',
);

// Post content
$post_content = '<p>[Instrumental]
E/D# C#m7 C#m/B A B E</p>
<p>[Verse 1]
Em          D/F#         G
Let\'s get down to business
D/F# G      Am          D
To defeat the Huns
Em          D/F#         G
Did they send me daughters
D/F# G      Am          D
When I asked for sons?
C/E
You\'re the saddest bunch
D/F#
I ever met
G
But you can bet
C
Before we\'re through
D
Mister, I\'ll make a man
Em D Em
out of you</p>
<p>[Verse 2]
Em          D/F#         G
Tranquil as a forest
D/F# G      Am          D
But on fire within
Em          D/F#         G
Once you find your center
D/F# G      Am          D
You are sure to win
C/E
You\'re a spineless, pale
D/F#
pathetic lot
G
And you haven\'t
C
got a clue
D
Somehow I\'ll make a man
Em D Em
out of you</p>
<p>[Bridge]
C          D
I\'m never gonna catch
B/D#
Say good-bye to those
Em
who knew me
D/F#           G
Boy, was I a fool in school
C
for cutting gym
C
This guy\'s got \'em
D
scared to death
B/D#
Hope he doesn\'t see
Em
right through me
D/F#           G
Now I really wish that I
C
knew how to swim</p>
<p>[Chorus]
D C/E
(Be a man)
D/F#
We must be swift as
G
the coursing river
D C/E
(Be a man)
C          D
With all the force
B/D#  Em
of a great typhoon
D  C/E
(Be a man)
C          D
With all the strength
B/D#  Em
of a raging fire
C
Mysterious as the
D          Dsus2  Esus2
dark side of the moon</p>
<p>[Verse 3]
Fm          Eb/G    Ab
Time is racing toward us
Eb/G    Ab  Bbm    Eb
till the Huns arrive
Fm          Eb/G    Ab
Heed my every order
Eb/G Ab  Bbm    Eb
and you might survive
Db
You\'re unsuited for
Eb
the rage of war
Ab
So pack up, go home
Db
you\'re through
Eb
How could I make a man
Fm
out of you?</p>
<p>[Chorus]
Eb          Db/F
(Be a man)
Eb/G
We must be swift as
Ab
the coursing river
Eb Db/F
(Be a man)
Eb
With all the force
C/E           Fm
of a great typhoon
Eb Db/F
(Be a man)
Eb
With all the strength
of a raging fire
Db
Mysterious as the
Eb          Ebsus2  Fm9
dark side of the moon</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>