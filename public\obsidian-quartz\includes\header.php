<?php
// Ensure we have path constants available
if (!isset($paths) && isset($config)) {
    require_once ($base_url ?? '') . 'path-helper.php';
    $paths = initPaths($config, __FILE__);
}
?>
<header>
    <div class="container header-container">
        <div class="site-branding">
            <img src="<?php echo ($paths['base_path'] ?? ($base_url ?? '')) . 'img/rosegrandpasvg.svg'; ?>"
                 alt="A. A. Chips"
                 class="site-logo">
            <a href="<?php echo ($paths['home_path'] ?? ($base_url ?? '') . 'index.html'); ?>" class="site-title">
                <?php echo htmlspecialchars($config['site']['name'] ?? 'A. A. Chips'); ?>
            </a>
        </div>
        <nav class="nav">
            <ul>
                <li><a href="<?php echo ($paths['base_path'] ?? ($base_url ?? '')) . 'content/index.php'; ?>">Start Here</a></li>
                <li><a href="<?php echo ($paths['base_path'] ?? ($base_url ?? '')) . 'content/playlists/music-player.php'; ?>">Music Playlist</a></li>
                <li><a href="<?php echo ($paths['base_path'] ?? ($base_url ?? '')) . 'gallery.php'; ?>">Gallery</a></li>
                <li><a href="<?php echo ($paths['base_path'] ?? ($base_url ?? '')) . 'store.php'; ?>">Store</a></li>
                <li><a href="<?php echo ($paths['base_path'] ?? ($base_url ?? '')) . 'content/graph-view.php'; ?>">Node Map</a></li>
                <!-- Color switcher will be dynamically added here by JavaScript -->
            </ul>
        </nav>
    </div>
</header>
