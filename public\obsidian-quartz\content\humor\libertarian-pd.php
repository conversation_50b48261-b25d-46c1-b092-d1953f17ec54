<?php
// Auto-generated blog post
// Source: content\humor\libertarian-pd.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'L.P.D.: Libertarian Police Department';
$meta_description = '“Home Depot™ Presents the Police!®” I said, flashing my badge and my gun and a small picture of Ron Paul. “Nobody move unless you want to!” They didn’t.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'L.P.D.: Libertarian Police Department',
  'author' => 'Tom O\'Donnell',
  'date' => '03-14-2014',
  'excerpt' => '“Home Depot™ Presents the Police!®” I said, flashing my badge and my gun and a small picture of Ron Paul. “Nobody move unless you want to!” They didn’t.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\humor\\libertarian-pd.md',
);

// Post content
$post_content = '<p>This is a short story written by Tom O\'Donnell of The New Yorker:</p>
<p>I was shooting heroin and reading “The Fountainhead” in the front seat of my privately owned police cruiser when a call came in. I put a quarter in the radio to activate it. It was the chief.</p>
<p>“Bad news, detective. We got a situation.”</p>
<p>“What? Is the mayor trying to ban trans fats again?”</p>
<p>“Worse. Somebody just stole sixteen million dollars’ worth of bitcoins.”</p>
<p>The heroin needle practically fell out of my arm. “What kind of monster would do something like that? Bitcoins are the ultimate currency: virtual, anonymous, stateless. They represent true economic freedom, not subject to arbitrary manipulation by any government. Do we have any leads?”</p>
<p>“Not yet. But mark my words: we’re going to figure out who did this and we’re going to take them down … provided someone pays us a fair market rate to do so.”</p>
<p>“Easy, chief,” I said. “Any rate the market offers is, by definition, fair.”</p>
<p>He laughed. “That’s why you’re the best I got, Lisowski. Now you get out there and find those bitcoins.”</p>
<p>“Don’t worry,” I said. “I’m on it.”</p>
<p>I put a quarter in the siren. Ten minutes later, I was on the scene. It was a normal office building, strangled on all sides by public sidewalks. I hopped over them and went inside.</p>
<p>“Home Depot™ Presents the Police!®” I said, flashing my badge and my gun and a small picture of Ron Paul. “Nobody move unless you want to!” They didn’t.</p>
<p>“Now, which one of you punks is going to pay me to investigate this crime?” No one spoke up.</p>
<p>“Come on,” I said. “Don’t you all understand that the protection of private property is the foundation of all personal liberty?”</p>
<p>It didn’t seem like they did.</p>
<p>“Seriously, guys. Without a strong economic motivator, I’m just going to stand here and not solve this case. Cash is fine, but I prefer being paid in gold bullion or autographed Penn Jillette posters.”</p>
<p>Nothing. These people were stonewalling me. It almost seemed like they didn’t care that a fortune in computer money invented to buy drugs was missing.</p>
<p>I figured I could wait them out. I lit several cigarettes indoors. A pregnant lady coughed, and I told her that secondhand smoke is a myth. Just then, a man in glasses made a break for it.</p>
<p>“Subway™ Eat Fresh and Freeze, Scumbag!®” I yelled.</p>
<p>Too late. He was already out the front door. I went after him.</p>
<p>“Stop right there!” I yelled as I ran. He was faster than me because I always try to avoid stepping on public sidewalks. Our country needs a private-sidewalk voucher system, but, thanks to the incestuous interplay between our corrupt federal government and the public-sidewalk lobby, it will never happen.</p>
<p>I was losing him. “Listen, I’ll pay you to stop!” I yelled. “What would you consider an appropriate price point for stopping? I’ll offer you a thirteenth of an ounce of gold and a gently worn ‘Bob Barr ‘08’ extra-large long-sleeved men’s T-shirt!”</p>
<p>He turned. In his hand was a revolver that the Constitution said he had every right to own. He fired at me and missed. I pulled my own gun, put a quarter in it, and fired back. The bullet lodged in a U.S.P.S. mailbox less than a foot from his head. I shot the mailbox again, on purpose.</p>
<p>“All right, all right!” the man yelled, throwing down his weapon. “I give up, cop! I confess: I took the bitcoins.”</p>
<p>“Why’d you do it?” I asked, as I slapped a pair of Oikos™ Greek Yogurt Presents Handcuffs® on the guy.</p>
<p>“Because I was afraid.”</p>
<p>“Afraid?”</p>
<p>“Afraid of an economic future free from the pernicious meddling of central bankers,” he said. “I’m a central banker.”</p>
<p>I wanted to coldcock the guy. Years ago, a central banker killed my partner. Instead, I shook my head.</p>
<p>“Let this be a message to all your central-banker friends out on the street,” I said. “No matter how many bitcoins you steal, you’ll never take away the dream of an open society based on the principles of personal and economic freedom.”</p>
<p>He nodded, because he knew I was right. Then he swiped his credit card to pay me for arresting him.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>