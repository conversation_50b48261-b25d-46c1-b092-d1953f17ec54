<?php
// Auto-generated blog post
// Source: content\ptsd-myth\autonomy-sdm.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'I\'m a strong advocate for Supported Decision Making, a legal movement that champions individual autonomy. As an expert Advocate in the Decision-Making...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'I\'m a strong advocate for Supported Decision Making, a legal movement that champions individual autonomy. As an expert Advocate in the Decision-Making...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\autonomy-sdm.md',
);

// Post content
$post_content = '<p>I\'m a strong advocate for Supported Decision Making, a legal movement that champions individual autonomy. As an expert Advocate in the Decision-Making Process, I believe in providing clients with the best information to make informed decisions and support their needs across various aspects of life, be it medicine, law, education, housing, or custody.</p>
<p>Supported Decision Making acknowledges the client as the foremost expert on their own life. They choose who they trust to help them make decisions, and we respect their choices, even if we don\'t agree, as long as they aren\'t harming themselves or others or breaking laws. This contrasts with outdated models where individuals with disabilities were often seen as incapable, and decisions were made for them.</p>
<p>I highly recommend Jonathan Martini\'s lecture and four-part video series on Supported Decision Making by UC Davis Minds Institute. Martini, a lawyer, argues for our fundamental right to choose and against Guardianship and Conservatorship. The MINDS Institute channel also has extensive resources on Human Services, including Autism, ADHD, and Special Needs Planning. Watch the first part here: <a href="https://www.youtube.com/watch?v=Q8Na88Wz90I" class="external-link">https://www.youtube.com/watch?v=Q8Na88Wz90I</a></p>
<p>This principle of choice applies broadly. Regardless of differing opinions on specific treatments, individuals have the right to choose what\'s best for them, unless that right has been legally taken away. Our role is to affirm their autonomy and self-determination.</p>
<p>In my work, I\'ve supported refugees, domestic violence survivors, and special needs adults, primarily online. I provide information and technology support, which is crucial for safety and wellbeing in the digital age. I\'ve learned to yield my ego, listen to clients, and meet them where they are, treating them as experts in their own lives.</p>
<p>When faced with difficult situations, like a client using cannabis in a state where it\'s illegal, the focus remains on supporting their decision-making. Unless there\'s evidence of harm, abuse, or exploitation, it\'s not our place to force abstinence. Decades of data show that this approach is ineffective. Interventions only work when the individual is a willing participant.</p>
<p>As an IT Helpdesk worker, I know the importance of empowering clients to solve their own problems. This principle extends to all areas of advocacy. We provide information and support, but the ultimate decision rests with the individual.</p>
<p>In harm reduction, agencies work with clients who use illicit drugs daily. They are not obligated to report them. We prioritize safety, screening for factors that contribute to addiction. Forcing abstinence can alienate individuals and push them further into addiction.</p>
<p>Choice is paramount. Our job is to help people make informed, supported decisions for themselves. <a href="https://www.npr.org/2021/06/17/1006495476/after-50-years-of-the-war-on-drugs-what-good-is-it-doing-for-us" class="external-link">https://www.npr.org/2021/06/17/1006495476/after-50-years-of-the-war-on-drugs-what-good-is-it-doing-for-us</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>