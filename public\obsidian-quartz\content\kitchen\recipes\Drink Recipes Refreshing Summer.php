<?php
// Auto-generated blog post
// Source: content\kitchen\recipes\Drink Recipes Refreshing Summer.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '**Hibiscus Tea: A Drink with Deep Roots**';
$meta_description = 'Hibiscus Tea: A Drink with Deep Roots Recipe Up Front, No Scrolling:   Ingredients: - 4 cups water      - ½ cup dried hibiscus flowers or 1 cup fresh...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '**Hibiscus Tea: A Drink with Deep Roots**',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Hibiscus Tea: A Drink with Deep Roots Recipe Up Front, No Scrolling:   Ingredients: - 4 cups water      - ½ cup dried hibiscus flowers or 1 cup fresh...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\recipes\\Drink Recipes Refreshing Summer.md',
);

// Post content
$post_content = '<h3><strong>Hibiscus Tea: A Drink with Deep Roots</strong></h3>
<p><strong>Recipe (Up Front, No Scrolling):</strong>  
_Ingredients:_</p>
<p>- 4 cups water
    
- ½ cup dried hibiscus flowers (or 1 cup fresh Thai roselle calyces)
    
- Sweetener to taste (sugar, honey, etc.)
    
- ½ tsp rose water (optional, added after cooling)</p>
<p>_Instructions:_</p>
<p>1. Boil water. Add hibiscus flowers.
    
2. Sweeten while hot, stir, then steep for 10–15 minutes.
    
3. Strain, cool, and add rose water if using. Serve over ice.</p>
<p><strong>The Story in the Soil:</strong>  
I grow Thai roselle in Appalachia—a plant that blurs the line between hibiscus and okra, with calyces that bleed a tart, cranberry-red brew. It thrives here, just as it does in West Africa, where it’s been cultivated for centuries. There’s a theory that the Appalachian Mountains once kissed the same tectonic plate as West Africa, before continents drifted apart. Now, the plants whisper across time: hibiscus in my garden, roselle in a Ghanaian market, all descendants of seeds carried through unspeakable violence.</p>
<p>Enslaved Africans smuggled seeds in their hair, in hems, in the hollows of hope. Those seeds became survival, then tradition, then joy. When I steep these flowers, I think of that resilience—how a drink can be both ordinary and sacred. Sip it sweetened, iced, or with a dash of rose water (a nod to the Arab traders who wove hibiscus into their pharmacopeia). It’s a small act of remembrance.</p>
<p>_(Insert photo: Thai roselle harvest, crimson calyces piled in a basket.)_</p>
<p>---</p>
<h3><strong>Doogh (Ayran): The Yogurt Drink That Converts Skeptics</strong></h3>
<p><strong>Recipe (Straight to the Point):</strong>  
_Ingredients:_</p>
<p>- 1 cup plain yogurt (whole-milk or labneh for richness)
    
- 1 cup cold water
    
- ¼ tsp salt
    
- Ice cubes
    
- Optional: dried mint, cucumber shreds, or a splash of sparkling water</p>
<p>_Instructions:_</p>
<p>1. Whisk yogurt and water until smooth.
    
2. Add salt. Pour over ice. Fizz it up with sparkling water if you’re feeling fancy.</p>
<p><strong>Why It’s More Than “Strange Yogurt Water”:</strong>  
Doogh (Persian) or ayran (Arabic) is the ultimate test of trust. “You’ll like it,” I promise friends, watching their faces twist at the idea of salty, diluted yogurt. Then—the inevitable second sip. The third. Soon, they’re gulping it down, hooked on its lactic tang, the way it cuts through heat and grease like a culinary exorcism.</p>
<p>The magic is in the yogurt. Some Middle Eastern restaurants guard their cultures like heirlooms, fermenting the same batch for decades. It’s alive, evolving. That’s the lesson here: food is never static. It’s migration, adaptation, the unbroken chain of hands that knead, stir, and serve.</p>
<p>And yes, it’s political. To love a culture’s food is to reject the lie of its inferiority. So whisk this boldly. Add mint if you want, or cucumber, or nothing at all. Drink it with kebabs, with fries, with a side of reckoning.</p>
<p>_(Insert photo: Glass of doogh beaded with condensation, a sprig of mint leaning lazily against the rim.)_</p>
<p>---</p>
<p><strong>Final Note:</strong> Both posts end with a call to action—e.g., “Tag your hibiscus experiments #SeedsWeCarry” or “Try doogh with falafel and tell me it’s not genius.” Lets the education (and unlearning) continue in the comments.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>