<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Holly Near - It Could Have Been Me Lyrics.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Holly Near - It Could Have Been Me Lyrics';
$meta_description = 'Holly Near - It Could Have Been Me Lyrics Artist: Holly Nearhttps://www.songlyrics.com/holly-near-lyrics/ "Holly Near Lyrics" Album: And Still We Si...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Holly Near - It Could Have Been Me Lyrics',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Holly Near - It Could Have Been Me Lyrics Artist: Holly Nearhttps://www.songlyrics.com/holly-near-lyrics/ "Holly Near Lyrics" Album: And Still We Si...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Holly Near - It Could Have Been Me Lyrics.md',
);

// Post content
$post_content = '<h1>Holly Near - It Could Have Been Me Lyrics</h1>
<p>Artist: <a href="https://www.songlyrics.com/holly-near-lyrics/ "Holly Near Lyrics"" class="external-link">Holly Near</a></p>
<p>Album: <a href="https://www.songlyrics.com/holly-near/and-still-we-sing-the-outspoken-collection/ "And Still We Sing: The Outspoken Collection Album Lyrics"" class="external-link">And Still We Sing: The Outspoken Collection</a></p>
<p>Heyo! SONGLYRICS just got interactive. Highlight. Review: RIFF-it.  
<a href="https://www.songlyrics.com/about.php" class="external-link">RIFF-it good.</a></p>
<p><a href="https://www.youtube.com/watch?v=CadP4dRemYk "Holly Near - It Could Have Been Me"" class="external-link">Listen while you read!</a></p>
<p>CHORUS:  
IT COULD HAVE BEEN ME, BUT INSTEAD IT WAS YOU  
SO I\'LL KEEP DOING THE WORK YOU WERE DOING AS IF I WERE TWO  
I\'LL BE A STUDENT OF LIFE, A SINGER OF SONGS  
A FARMER OF FOOD AND A RIGHTER OF WRONG  
IT COULD HAVE BEEN ME, BUT INSTEAD IT WAS YOU  
AND IT MAY BE ME DEAR SISTERS AND BROTHER  
BEFORE WE ARE THROUGH  
BUT IF YOU CAN WORK FOR FREEDOM  
FREEDOM, FREEDOM, FREEDOM  
IF YOU CAN WORK (LIVE, DIE, SING) FOR FREEDOM I CAN TOO  
  
VERSE:  
STUDENTS IN OHIO AT KENT AND JACKSON STATE  
SHOT DOWN BY A NAMELESS ( or VICIOUS) FIRE ONE EARLY DAY IN MAY  
SOME PEOPLE CRIED OUT ANGRY YOU SHOULD HAVE SHOT MORE OF THEM DOWN  
BUT YOU CAN\'T BURY YOUTH MY FRIEND  
YOUTH GROWS THE WHOLE WORLD ROUND  
  
CHORUS;  
IF YOU CAN DIE FOR FREEDOM I CAN TOO  
  
VERSE:  
THE JUNTA BROKE THE FINGERS ON VICTOR JARA\'S HANDS  
THEY SAID TO THE GENTLE POET "PLAY YOUR GUITAR NOW IF YOU CAN"  
VICTOR STARTED SINGING BUT THEY BROUGHT HIS BODY DOWN  
YOU CAN KILL THAT MAN BUT NOT HIS SONG  
WHEN IT\'S SUNG THE WHOLE WORLD ROUND  
  
CHORUS:  
IF YOU CAN SING FOR FREEDOM I CAN TOO  
  
VERSE:  
A WOMAN IN THE JUNGLE SO MANY WARS AWAY  
STUDIES LATE INTO THE NIGHT, DEFENDS THE VILLAGE IN THE DAY  
ALTHOUGH HER SKIN IS GOLDEN LIKE MINE WILL NEVER BE  
HER SONG IS HEARD AND I KNOW THE WORDS  
AND I\'LL SING THEM UNTIL SHE\'S FREE  
  
CHORUS:  
IF YOU CAN LIVE FOR FREEDOM I CAN TOO  
  
ONE NIGHT IN OKLAHOMA KAREN SILKWOOD DIED  
BECAUSE SHE HAD SOME SECRETS THAT BIG COMPANIES WANTED TO HIDE  
THERE\'S TALK OF NUCLEAR SAFETY AND THERE\'S TALK OF NATIONAL PRIDE  
BUT WE ALL KNOW IT IS A DEATH MACHINE AND THAT\'S WHY KAREN DIED  
  
CHORUS:  
IF YOU CAN SPEAK FOR FREEDOM I CAN TOO  
  
THE SONGS OF NICARAGUA AND EL SALVADOR  
WILL LONG OUTLAST THE SINGERS WHO FACE THE GUNS IN WAR  
THEY SING AT THE LINE OF FIRE, THEY SING FROM THE FIRE WITHIN  
ALL ACROSS THE LAND THE POETS STAND  
(SPOKEN IN PLACE OF MELODY)  
EL PUEBLO UNIDO JAMAS SERA VENCIDO  
EL PUEBLO UNIDO JAMAS SERA VENCIDO</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>