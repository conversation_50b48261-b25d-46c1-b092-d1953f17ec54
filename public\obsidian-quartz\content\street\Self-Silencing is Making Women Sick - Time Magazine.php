<?php
// Auto-generated blog post
// Source: content\street\Self-Silencing is Making Women Sick - Time Magazine.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Self-Silencing Is Making Women Sick';
$meta_description = 'Self-Silencing Is Making Women Sick 6 minute read !https://api.time.com/wp-content/uploads/2023/09/annano.jpg?quality=85&w=2400 "annano" Illustration...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Self-Silencing Is Making Women Sick',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Self-Silencing Is Making Women Sick 6 minute read !https://api.time.com/wp-content/uploads/2023/09/annano.jpg?quality=85&w=2400 "annano" Illustration...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\street\\Self-Silencing is Making Women Sick - Time Magazine.md',
);

// Post content
$post_content = '<h1>Self-Silencing Is Making Women Sick</h1>
<p>6 minute read</p>
<p>![](https://api.time.com/wp-content/uploads/2023/09/anna_no.jpg?quality=85&w=2400 "anna_no")</p>
<p>Illustration by Anna Parini for TIME</p>
<p>Ideas</p>
<p>By  <a href="https://time.com/author/maytal-eyal/" class="external-link">Maytal Eyal</a></p>
<p>October 3, 2023 7:00 AM EDT</p>
<p>Eyal is a writer and psychologist. Her work has appeared in The Atlantic, Wired, and Psychology Today. She is currently writing a book on how therapy culture lost its way</p>
<p>“Be more disappointing” is not a piece of advice most people would pay money to hear, but in my therapy office, it’s often the most valuable guidance I can give. My clients are mostly women, and nearly all of them struggle with a fear of disappointing others. Our culture rewards women for being perpetually pleasant, self-sacrificing, and emotionally in control, and it can feel counterintuitive for my clients to say “no”—or firmly assert their wants and needs. But my work is about helping them realize that their health might literally depend on it.</p>
<p>Today, women account for almost <a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC7292717/#:~:text=Eighty%20percent%20of%20all%20individuals,sex%20chromosomes%20and%20hormonal%20changes." class="external-link">80% of autoimmune disease</a> cases. They are at a higher risk of suffering from <a href="https://www.iasp-pain.org/advocacy/global-year/pain-in-women/#:~:text=Female%20Pain%20Issues,-Pain%20conditions%20affecting&text=Research%20has%20shown%20that%20women,longer%20lasting%20pain%20than%20men." class="external-link">chronic pain</a>, <a href="https://academic.oup.com/sleep/article/29/1/85/2708069?login=false" class="external-link">insomnia</a>, <a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC6425926/" class="external-link">fibromyalgia</a>, <a href="https://www.cdc.gov/nchs/pressroom/nchs_press_releases/2022/********.htm" class="external-link">long COVID</a>, <a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC6175559/" class="external-link">irritable bowel syndrome</a>, and <a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC7704513/" class="external-link">migraines</a>, and are twice as likely as men to <a href="https://www.healthline.com/health-news/why-women-are-more-likely-to-die-after-a-heart-attack#Heart-attack-symptoms-in-women" class="external-link">die after a heart attack</a>. Women experience <a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC4478054/" class="external-link">depression</a>, <a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC3135672/" class="external-link">anxiety</a>, and <a href="https://www.nami.org/Blogs/NAMI-Blog/October-2019/PTSD-is-More-Likely-in-Women-Than-Men" class="external-link">PTSD</a> at twice the rate of men, and face a ninefold higher prevalence of <a href="https://pubmed.ncbi.nlm.nih.gov/********/#:~:text=Anorexia%20nervosa%20\\(AN\\" class="external-link">anorexia</a>%20occurs%20nine,with%20anthropometric%20and%20metabolic%20traits.), the <a href="https://www.psychologytoday.com/us/blog/the-new-brain/201103/the-deadliest-disorder-0" class="external-link">deadliest</a> mental health disorder.</p>
<p>Why is it that women are falling ill to these diseases at a rate so much higher than men? Such jarring disparities cannot be accounted for by genetic and hormonal factors alone; psychosocial factors play an important role as well. Specifically, it seems that the very virtues our culture rewards in women—agreeability, extreme selflessness, and suppression of anger—may predispose us to chronic illness and disease.</p>
<p>_<strong>Read More:</strong> <a href="https://time.com/6314076/case-for-mediocrity/" class="external-link">The Case for Mediocrity</a>_</p>
<p>In the late 1980s, Harvard-trained psychologist Dana Jack identified a recurring theme among female patients suffering from depression: a tendency to self-silence, <a href="https://citeseerx.ist.psu.edu/document?repid=rep1&type=pdf&doi=8fc0e564aec17bf8b705635a057f40b7b8505f2a" class="external-link">defined</a> as “the propensity to engage in compulsive caretaking, pleasing the other, and inhibition of self-expression in relationships in an attempt to achieve intimacy and meet relational needs.” Through <a href="https://blog.oup.com/2010/03/silencing-the-self/" class="external-link">longitudinal research</a>, Jack found that this learned behavior, strongly rooted in gender norms, was linked to an increased risk of depression.</p>
<p>Since then, considerable evidence has revealed that female self-silencing isn’t just tied to psychological issues like depression and <a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC2247456/" class="external-link">eating disorders</a>, but also to physical illness. For instance, in March of 2022 a team of researchers at the University of Pittsburgh <a href="https://www.ncbi.nlm.nih.gov/pmc/articles/PMC8887577/" class="external-link">discovered</a> that women of color who strongly agreed with statements like “I rarely express my anger to those close to me,” were 70% more likely to experience increased carotid atherosclerosis, a cardiovascular plaque associated with higher risk of heart attack. <a href="https://journals.sagepub.com/doi/pdf/10.1177/0020764018814271" class="external-link">Other studies</a> have connected self-silencing to irritable bowel syndrome, HIV, chronic fatigue syndrome, and cancer among women.</p>
<p>Most jarringly, women’s self-silencing has also been linked to higher risk of premature death. In one <a href="https://books.google.com/books?hl=en&lr=&id=Y39MCAAAQBAJ&oi=fnd&pg=PA399&ots=I5TNawDl1c&sig=PeEfN939i55wkneI_J6YSTb6KbI#v=onepage&q&f=false" class="external-link">study</a>, researchers followed nearly 4,000 people in Framingham, Massachusetts over 10 years. They found that women who didn’t express themselves when they had fights with their spouses were four times more likely to die than those who did. [](https://www.ncbi.nlm.nih.gov/pmc/articles/PMC3939772/)This was true even when factors such as age, blood pressure, smoking, and levels of cholesterol were taken into account.</p>
<p>When women push their feelings down and cast their needs aside, their health suffers. But it can be difficult for women to do otherwise in a culture that celebrates these self-silencing practices. While young women are praised for “being chill,” moms are revered for being painstakingly altruistic to the point of self-abnegation. These unspoken standards establish a vicious cycle. For many women, it feels easier—beneficial, even—to silence their needs at the expense of their own health, rather than swim against the prevailing cultural current.</p>
<p>In his best-selling book, <a href="https://drgabormate.com/book/the-myth-of-normal/" class="external-link">_The Myth of Normal_</a>, physician and author Gabor Mate writes that many of our society’s most “normalized ways of being”—the qualities we regard as “admirable strengths rather than potential liabilities”—are, in fact, incredibly toxic. “That ‘not listening to self’ in order to prioritize others’ needs is a significant source of the health-impairing roles women assume,” Mate explains. “It is among the medically overlooked but pernicious ways in which our society’s ‘normal’ imposes a major health cost on women.”</p>
<p>It seems that the virtues of womanhood are not really virtuous after all; instead, they are wreaking havoc on our bodies and our health. And the way they often do so is through these seemingly “normal,” daily experiences that slowly, over time, chip away at our vitality and erode our well-being. My clients tell me things like, “I don’t deserve to put my needs first. I’m not the breadwinner,” or “I said ‘yes,’ even though I didn’t want to.” In their gradual attempt to be what society considers “good,” they run the risk of compromising their health.</p>
<p>As a psychologist, it can sometimes feel challenging to help my clients take back their emotional and physical health when they are contending against a complex cultural system that is reinforcing them to do the opposite. However, I have found that there are some tangible changes that really do, in practice, make a difference.</p>
<p>It can be paradigm shifting to understand that behind every emotion exists a need. Anger, for example, can signify the desire to change our current circumstances. Rather than women treating our emotions as inconvenient, bodily malfunctions best to be muted and ignored, we can teach ourselves to view them as windows of insight. Instead of casting away our anger, a valuable question we can ask ourselves in moments of frustration is: what am I needing right now?</p>
<p>Another practice, closely related, is boundary setting. For women, who have been unconsciously taught to view our likability as our greatest asset, boundary setting can often feel counterintuitive. Many of us fear that if we honestly communicate our needs and limitations, this will threaten our relationships. But it’s the contrary that’s true: when we set heathy boundaries (rather than toxic ones that can lead to <a href="https://time.com/6271915/self-love-loneliness/" class="external-link">radical individualism</a>) our relationships actually become stronger and healthier. And having healthy relationships is integral to our physical well-being; one <a href="https://pubmed.ncbi.nlm.nih.gov/20668659/" class="external-link">meta-analysis</a> showed that people with more supportive social relationships have a 50% lower risk of premature death.</p>
<p>To reshape the virtues of womanhood, a new “normal” needs to emerge—one in which we honor our emotions, prioritize our needs, and actively communicate our boundaries. Such a shift requires change on both the individual and societal level, and will by no means by easy. But it’s certainly worth it—after all, women’s lives depend on it.</p>
<h2>More Must-Reads from TIME</h2>
<p>- <a href="https://time.com/7268032/doge-cybersecurity-elon-musk/?utm_source=roundup&utm_campaign=20230202" class="external-link">Cybersecurity Experts</a> Are Sounding the Alarm on DOGE
- Meet the <a href="https://time.com/collection/women-of-the-year/?utm_source=roundup&utm_campaign=20230202" class="external-link">2025 Women of the Year</a>
- The Harsh Truth About <a href="https://time.com/7261675/disability-inclusion-dei-cuts-essay/?utm_source=roundup&utm_campaign=20230202" class="external-link">Disability Inclusion</a>
- Why Do More <a href="https://time.com/7213490/why-are-young-people-getting-cancer/?utm_source=roundup&utm_campaign=20230202" class="external-link">Young Adults Have Cancer?</a> 
- <a href="https://time.com/7210621/actor-colman-domingo-radical-love/?utm_source=roundup&utm_campaign=20230202" class="external-link">Colman Domingo</a> Leads With Radical Love
- How to Get Better at <a href="https://time.com/7205297/how-to-do-things-alone-mental-health/?utm_source=roundup&utm_campaign=20230202" class="external-link">Doing Things Alone</a>
- <a href="https://time.com/7266341/michelle-zauner-interview-japanese-breakfast-for-melancholy-brunettes/?utm_source=roundup&utm_campaign=20230202" class="external-link">Michelle Zauner</a> Stares Down the Darkness</p>
<p>Contact us at <a href="mailto:<EMAIL>" class="external-link"><EMAIL></a></p>
<p><a href="https://www.time.com/ideas" class="external-link">TIME Ideas</a> hosts the world\'s leading voices, providing commentary on events in news, society, and culture. We welcome outside contributions. Opinions expressed do not necessarily reflect the views of TIME editors.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>