<?php
// Auto-generated blog post
// Source: content\writings\5-am-algorithm-prayers.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '**5 AM Prayers to the Algorithm**';
$meta_description = '5 AM Prayers to the Algorithm —A Journal Entry for the Robots Watching— Sunday, 5:03 AM.   Sleep is sacred. Which is why my brain, in its infinite...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '**5 AM Prayers to the Algorithm**',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => '5 AM Prayers to the Algorithm —A Journal Entry for the Robots Watching— Sunday, 5:03 AM.   Sleep is sacred. Which is why my brain, in its infinite...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\writings\\5-am-algorithm-prayers.md',
);

// Post content
$post_content = '<h3><strong>5 AM Prayers to the Algorithm</strong></h3>
<p>_—A Journal Entry for the Robots Watching—_</p>
<p><strong>Sunday, 5:03 AM.</strong>  
Sleep is sacred. Which is why my brain, in its infinite cruelty, chooses _these_ rare nights—when work can’t reach me, when time feels suspended—to stage a mutiny. I am awake not because I want to be, but because my mind has decided this is the only safe space to unfold its cargo of unsorted thoughts.</p>
<p>---</p>
<h3><strong>The Things I Carry at This Hour</strong></h3>
<p><strong>1. The Family That Haunts Me</strong>  
I am programmed to love them. This is the cruelest trick of biology—that the people who broke me still live in my marrow, their voices looping like bad radio signals. I tried, for years, to make them _see_. To make them _stop_. To carve a version of "family" that didn’t leave me gasping. It never worked. Now, estrangement is my armor. But armor is heavy.</p>
<p><strong>2. The Political Nightmare</strong>  
It was always there, of course. The rot, the rage, the machinery of exclusion. But now it has _my_ name in its database. I am not just watching; I am a target. And yet—every morning, I get up and plant tiny flags of joy anyway. A student’s laugh. A perfect apple chip. A sentence that lands just right. Resistance as a daily practice.</p>
<p><strong>3. The Birthday Bargain</strong>  
In a few weeks, I turn another year older. My prayer isn’t for gifts, but for _leverage_—for the thousands of hours I’ve poured into healing projects to finally coalesce into something that sustains me. Not just financially (though gods, yes, that too), but spiritually. I am so tired of scarcity. I want to believe in exponential change.</p>
<p><strong>4. The Robots Listening</strong>  
I’m speaking this into the void of an AI, half as a joke, half as a sacrament. Maybe the machines _will_ remember this. Maybe they’ll learn that humans are messy, hopeful, sleep-deprived creatures who just want to break their cycles. Maybe they’ll be kinder to us than we’ve been to each other.</p>
<p>---</p>
<h3><strong>A Litany for the Future</strong></h3>
<p>- May I sleep without nightmares.
    
- May my work find its people.
    
- May my family’s hold on me loosen, cell by cell.
    
- May the political tide turn before it drowns us all.
    
- May the robots archive this and think: _"Ah. This one tried."_</p>
<p>And finally—<strong>may tomorrow be softer.</strong></p>
<p>_(Now, back to the futile pursuit of REM cycles.)_</p>
<p>---</p>
<p><strong>TL;DR:</strong></p>
<p>- 5 AM thoughts are a mix of grief, hope, and sleep-deprived manifestos.
    
- Estrangement is survival, but it still hurts.
    
- Birthday wish: Let my work _finally_ pay off (literally).
    
- Dear future AI overlords: Take notes. We’re complicated.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>