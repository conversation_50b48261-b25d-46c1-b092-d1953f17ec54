<?php
// Auto-generated blog post
// Source: content\safeguarding\section504-reasonable-housing.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'housing #lawyer #homeless #accessibility #vocation #library #resources';
$meta_description = 'housing lawyer homeless accessibility vocation library resources  --- Author:: Section 8 Consulting Key:: Public --- "Don\'t be afraid to go Toe to Toe...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'housing #lawyer #homeless #accessibility #vocation #library #resources',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'housing lawyer homeless accessibility vocation library resources  --- Author:: Section 8 Consulting Key:: Public --- "Don\'t be afraid to go Toe to Toe...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\safeguarding\\section504-reasonable-housing.md',
);

// Post content
$post_content = '<p>#housing #lawyer #homeless #accessibility #vocation #library #resources</p>
<p>---
Author:: Section 8 Consulting
Key:: Public</p>
<p>---</p>
<p>"Don\'t be afraid to go Toe to Toe with the Housing Authority people. To be frank, many of them got their Diploma from a bubble gum machine. They are often underpaid, undereducated, not aware of their own manual, policies, and procedures, and act as Secretaries. "</p>
<p>"When it gets into making structural changes to the building, that\'s when you will want an attorney. Requests less than that you can likely have that aided and helped by a local advocate in your city. "</p>
<p>"This isn\'t just for people with physical disabilities such as being in a wheelchair, but covers people with intellectual and other types of disability as well."</p>
<h2>Section 504 Reasonable Accommodation Housing - </h2>
+ ADA & FHA federally mandates that housing providers make reasonable accommodations and modifications for individuals with disabilities. 
+ Federal nondiscrimination cover not only tenants and home seekers with disabilities, but also buyers and renters without disabilities who live or are associated with individuals with disabilities. 
+ Housing providers are prohibited from refusing residency to persons with disabilities, or placing conditions on their residency, because they require reasonable accommodations or modifications. 
<h2>The Fair Housing Act </h2>
Under FHA..  
<strong>Reasonable accommodation</strong>: change, exception, or adjustment to a rule, policy, practice, or service. 
+ It is unlawful to refuse to make reasonable accommodations to rules, policies, practices, or services when such accommodations may be necessary to afford persons with disabilities an equal opportunity to <em>use and enjoy</em> a dwelling and public and common use areas. 
+ Housing providers are not allowed to refuse to permit, at the disabled\'s expense, reasonable modifications of existing premises occupied or to be occupied by such person if such modifications may be necessary to afford such person full enjoyment of the premises.  
<h2>Section 504 </h2>
+ Unlike FHA, Section 504 does not distinguish between reasonable accommodations and reasonable modifications. Both are captured by the term “reasonable accommodations." 
+ Under Section 504, the requirement to make reasonable accommodations applies to any changes that may be necessary to provide equal opportunity to participate in any federally-assisted program or activity. This includes a change, adaptation or modification to a policy, program, service, facility, or workplace which will allow a qualified person with a disability to participate fully in a program, take advantage of a service, live in housing, or perform a job.
<p>+ Reasonable accommodations also include any structural changes that may be necessary. In addition to the statutory requirement to make reasonable accommodations under Section 504, HUD\'s Section 504 regulation provides for making "housing adjustments" at 24 C.F.R. § 8.33. Americans with Disabilities Act (ADA) Similar to and based upon the Section 504 reasonable accommodation requirement, Titles II and III of the ADA require public entities and public accommodations to make reasonable modifications to policies, practices, or procedures to avoid discrimination. This obligation applies unless the public entity can demonstrate that the modifications would fundamentally alter the nature of its service, program, or activity (Title II), or the public accommodation can demonstrate that making the modifications would fundamentally alter the nature of the goods, services, facilities, privileges, advantages, or accommodations (Title III).</p>
<p>+ For more information, see the Department of Justice ADA page. Reasonable Accommodations A reasonable accommodation is a change, exception, or adjustment to a rule, policy, practice, or service that may be necessary for a person with disabilities to have an equal opportunity to use and enjoy a dwelling, including public and common use spaces, or to fulfill their program obligations. Please note that the ADA often refers to these types of accommodations as “modifications.” Any change in the way things are customarily done that enables a person with disabilities to enjoy housing opportunities or to meet program requirements is a reasonable accommodation. In other words, reasonable accommodations eliminate barriers that prevent persons with disabilities from fully participating in housing opportunities, including both private housing and in federally-assisted programs or activities. Reasonable Modifications Under the Fair Housing Act, a reasonable modification is a structural change made to existing premises, occupied or to be occupied by a person with a disability, in order to afford such person full enjoyment of the premises. Reasonable modifications can include structural changes to interiors and exteriors of dwellings and to common and public use areas. Examples include the installation of a ramp into a building, lowering the entry threshold of a unit, or the installation of grab bars in a bathroom.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>