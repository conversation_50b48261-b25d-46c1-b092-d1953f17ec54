<?php
// Auto-generated blog post
// Source: content\journal\approach-consumption.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'My Approach to Consumption - A. A. Chips';
$meta_description = 'When asked about upcoming purchases, I struggle to answer—not because I can\'t afford things, but because buying isn\'t central to my lifestyle. Living below the poverty line for most of my adult life has shaped a deliberately minimal approach to consumption.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'My Approach to Consumption - A. A. Chips',
  'author' => 'A. A. Chips',
  'date' => '2025-02-24',
  'excerpt' => 'When asked about upcoming purchases, I struggle to answer—not because I can\'t afford things, but because buying isn\'t central to my lifestyle. Living below the poverty line for most of my adult life has shaped a deliberately minimal approach to consumption.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\approach-consumption.md',
);

// Post content
$post_content = '<p>When asked about upcoming purchases, I struggle to answer—not because I can\'t afford things, but because buying isn\'t central to my lifestyle. Living below the poverty line for most of my adult life has shaped a deliberately minimal approach to consumption.</p>
<h2>How I Meet Basic Needs Without Traditional Spending</h2>
<h3>Food</h3>
<p>- I cook most meals as a professional home cook
- I participate in free community meal programs
- My apple chip business uses surplus fruit that would otherwise go to waste
- In earlier years, I rescued discarded food through dumpster diving
- Now I work with nonprofits to legitimately redirect food waste</p>
<h3>Transportation</h3>
<p>- I drive less than 10,000 miles yearly in a low-maintenance vehicle
- Regular maintenance happens at a trusted local shop that doesn\'t invent problems
- I focus on necessary repairs only—oil changes, occasional tire replacements, brake pads</p>
<h3>Household & Personal Items</h3>
<p>- I help people clear out homes and storage units when they leave town
- In exchange, I redistribute their unwanted belongings
- Last month, this yielded clothes, books, art supplies, and even a PlayStation 3
- Most items I receive are given away to others in need</p>
<h3>Financial Approach</h3>
<p>- I maintain zero debt
- My part-time job covers basic monthly expenses
- My small business fills occasional financial gaps
- I practice extreme intentionality with spending</p>
<h3>Leisure & Self-Care</h3>
<p>- Annual camping trip replaces expensive vacations
- I haven\'t flown or stayed in hotels in years
- I manage health through nutrition, rest, and preventive practices
- I\'ve adapted to minimal healthcare access despite having pre-existing conditions</p>
<h2>My Essential Purchases</h2>
<p>Despite my minimal consumption habits, two unavoidable expenses remain central to my life:</p>
<p>1. <strong>Housing</strong>: Monthly rent ensures I have safe, stable shelter
2. <strong>Transportation</strong>: Occasional gas fill-ups keep my car running for essential travel</p>
<h2>The Bigger Picture</h2>
<p>This lifestyle isn\'t about deprivation—it\'s about intention. By stepping outside consumer culture, I\'ve found greater freedom and resilience. My approach might seem extreme to some, but it offers a perspective on what\'s truly necessary versus what\'s merely expected in our consumption-driven society.</p>
<p><img src="../../img/edutainment/possum-living.png" width="250" alt="Possum Living Book by Dolly Freed"></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>