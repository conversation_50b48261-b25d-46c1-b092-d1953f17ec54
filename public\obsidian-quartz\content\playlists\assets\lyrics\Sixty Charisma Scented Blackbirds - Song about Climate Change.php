<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Sixty Charisma Scented Blackbirds - Song about Climate Change.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '[Sixty Charisma Scented Blackbirds](https://songmeanings.com/songs/view/3530822107859541573/ "Sixty Charisma Scented Blackbirds")';
$meta_description = 'This is a sweet song called When Time and Space Collide, by Gabriel Mayers. It is video-recorded and directed into the documentary How to Let Go of th...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '[Sixty Charisma Scented Blackbirds](https://songmeanings.com/songs/view/3530822107859541573/ "Sixty Charisma Scented Blackbirds")',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'This is a sweet song called When Time and Space Collide, by Gabriel Mayers. It is video-recorded and directed into the documentary How to Let Go of th...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Sixty Charisma Scented Blackbirds - Song about Climate Change.md',
);

// Post content
$post_content = '
<p>This is a sweet song called When Time and Space Collide, by Gabriel Mayers. It is video-recorded and directed into the documentary How to Let Go of the World, by Josh Fox. In the documentary, this song is played after looking at the lasting devastation in parts of New York from Hurricane Exxon. It’s performed in the New York Subway, at times to footage of flood and high water in NYC. If there is a way to watch the documentary for free online, I would highly recommend this if we do a week on Climate. </p>
<p><a href="https://www.youtube.com/watch?v=lmgSAed0wH8" class="external-link">When time and space collide (Sixty Charisma Scented Blackbirds)</a></p>

<p>**
<h1><a href="https://songmeanings.com/songs/view/3530822107859541573/ "Sixty Charisma Scented Blackbirds"" class="external-link">Sixty Charisma Scented Blackbirds</a></h1></p>
<h3><a href="https://songmeanings.com/artist/view/songs/137439069246/ "Gabriel Mayers"" class="external-link">Gabriel Mayers</a></h3>
<p>I don\'t know what to say I feel lost and confused  
I want someone to love but everyone\'s being used  
Man I feel like a mess like I always feel down  
Just leave me alone but I need someone around  
I\'m on my last legs I need some sign of home  
Is it it all just a waste is my life just a joke  
  
When time a space collide I hope I\'m by your side  
When time a space collide I hope I\'m by your side  
  
I had it rough coming up I hope you can understand  
Always crying alone no one to hold my hand  
Well my daddy he left and my mama grew cold  
I just wanna make her proud before I\'m too old  
Before I\'m too weary before I\'ve lost hope  
Before it\'s too late for my dreams go up in smoke  
  
When time and space collide I hope I\'m by your side  
When time and space collide I hope I\'m by your side</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>