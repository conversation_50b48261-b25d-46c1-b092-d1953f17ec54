<?php
// Auto-generated blog post
// Source: content\journal\asylum-run-dream.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '5/21/2025 -';
$meta_description = '5/21/2025 -  Asylum Run I woke up in quiet concern and unsettle. The dreams were still there, sharp and unshaken. First, I was a patient in a condemne...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '5/21/2025 -',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => '5/21/2025 -  Asylum Run I woke up in quiet concern and unsettle. The dreams were still there, sharp and unshaken. First, I was a patient in a condemne...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\asylum-run-dream.md',
);

// Post content
$post_content = '
<p>5/21/2025 - 
<strong>Asylum Run</strong></p>
<p>I woke up in quiet concern and unsettle. The dreams were still there, sharp and unshaken.</p>
<p>First, I was a patient in a condemned asylum for "behaviorally challenged" children—a rotting operation still grinding along. They pumped us full of something that made the hallucinations worse, not better. The staff watched, blank-eyed, as we unraveled.</p>
<p>Then, I was running. Naked, from the top of the Ridges, my bare feet slapping cracked pavement. Staffers turned their heads as I passed, but I didn’t meet their stares. Just straight ahead, always ahead.</p>
<p>Later, I lived in my car—a small hatchback with a pop-up tent and no cooler. Water leaked in the back, a constant seep. There were people who could’ve helped, but the dream whispered the truth: _You don’t have the friends you had ten years ago._</p>
<p>I sat up. The room was quiet. My hands were steady.  
No breakdown. Just the ghost of a dream, and the day waiting.</p>



';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>