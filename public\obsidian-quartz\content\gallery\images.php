<?php
// Auto-generated blog post
// Source: content\gallery\images.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = '!jowy 2.jpg !kat.jpg !mowie-hammock.jpg !mowie-quilt.jpg !mowielake.jpg !oldroom.png !shiva.jpg The Best Dog in the Whole World !udismowie.jpg !VID201...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => '!jowy 2.jpg !kat.jpg !mowie-hammock.jpg !mowie-quilt.jpg !mowielake.jpg !oldroom.png !shiva.jpg The Best Dog in the Whole World !udismowie.jpg !VID201...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\gallery\\images.md',
);

// Post content
$post_content = '

<p>]]</p>
<p>![[jowy (2).jpg]]
![[kat.jpg]]
![[mowie-hammock.jpg]]
![[mowie-quilt.jpg]]
![[mowielake.jpg]]
![[oldroom.png]]
![[shiva.jpg]]
[[The Best Dog in the Whole World]]
![[udismowie.jpg]]
![[VID_20190823_145819749.mp4]]
![[april-garden.jpg]]
![[asthma.png]]
![[astral-rip.png]]
![[atuhe-rip.png]]
![[compostingtoilet.jpg]]
![[deviled-eggs.jpg]]
![[evgselfie.jpg]]
![[fernkitchen.jpg]]
![[flood5.jpg]]
![[greenopportunities.jpg]]
![[hair.jpg]]
![[headshot.jpg]]
![[honeycomb.png]]
![[IMG_20191219_180031138_HDR.jpg]]
![[IMG_20201022_091738223.jpg]]
![[jody.jpg]]
![[ladysunshinedinner.jpg]]
![[LEAF2.jpg]]
![[makin-brownies.jpg]]
![[meadow.jpg]]
![[menjody.jpg]]
![[monsterhigh2.jpg]]
![[mowie-and-dad.jpg]]
![[mowybag.jpg]]
![[new-phone.jpg]]
![[redreiver.jpg]]
![[rosettas.jpg]]
![[selfie1.jpg]]
![[shipment.jpg]]
![[so-heavy.jpg]]
![[tapestry.jpg]]
![[vermin.jpg]]
![[WIN_20180205_122151.jpg]]
![[zucchyini eyes.jpg]]
![[applepie.gif]]
![[badBreathing.png]]</p>
<p>![[nobody.jpg]]
[[Tapping into Personal Strength]]
![[UncleSeth.jpg]]</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>