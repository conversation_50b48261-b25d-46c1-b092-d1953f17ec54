<?php
// Auto-generated blog post
// Source: content\climate\ocean-plastic-breakthrough.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'We Finally Cracked the Code on Ocean Plastic';
$meta_description = 'Planet Wild - We Finally Cracked the Code on Ocean Plastic';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'We Finally Cracked the Code on Ocean Plastic',
  'author' => 'Planet Wild',
  'date' => '2025-05-20',
  'excerpt' => 'Planet Wild - We Finally Cracked the Code on Ocean Plastic',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\ocean-plastic-breakthrough.md',
);

// Post content
$post_content = '<h1>We finally cracked the code on ocean plastic</h1>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/Cl6HRJWomnM?si=KWKuDEVFq6qbLFYB" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<p><a href="https://yt3.ggpht.com/WvB90xbzgSmtFWi3EM4i-VyORSw5ynj46vwh4P9YAG1uDT0K62xTVATH8f9wYeuuL27bfnpkSQ=s88-c-k-c0x00ffffff-no-rj" class="external-link">![</a>](https://www.youtube.com/@planet-wild)</p>
<p><a href="https://www.youtube.com/@planet-wild" class="external-link">Planet Wild</a>
270K subscribers
570,391 views May 15, 2025</p>
<p><strong>Helpful Blog Post from Transcript of "We Finally Cracked the Code on Ocean Plastic"</strong></p>
<p>In this blog post, we discuss the transcript of a video from Planet Wild titled "We Finally Cracked the Code on Ocean Plastic". The video is the 27th mission of Planet Wild, a global community of people who care deeply about nature and want to help our planet bounce back. The mission is about stopping the plastic river in Albania and keeping it out of the sea for good.</p>
<p>The video starts with an introduction to the Drin River, a trash meeting point where the Black Drin River from North Macedonia and the White Drin from Kosovo come together. The river is polluted with huge amounts of trash, which is carried downstream by wind, rain, and drainage. The pollution ends up in the Mediterranean, which is a disaster because the Mediterranean only holds 1% of the world\'s water, but it contains 7% of all microplastics.</p>
<p>The video then introduces Everwave, a local partner that will help to stop the flow of trash. Everwave has a genius solution, which involves using an existing dam to trap huge amounts of trash in a reservoir. The trash is then collected by a boat that can collect one tonne of trash in under 20 minutes. The boat is modified from an aquatic wheat harvester and will be used to collect plastic from the reservoir.</p>
<p>The video also discusses the impact of the mission, which will be to remove a huge amount of plastic from the ocean before it even gets there. The ongoing work at the reservoir is a powerful proof of concept, and soon 25 tonnes of plastic will be removed every month, turning it into recyclable materials.</p>
<p>The blog post will also include information about Planet Wild, including how it works, and how people can join as backers.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>