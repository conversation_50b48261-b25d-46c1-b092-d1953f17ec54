<?php
// Auto-generated blog post
// Source: content\access-tech\Accommodated Employment Primer.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'dd #april #dme #disability #hhs #hse #homework #notes #project #professional #resources #revisit #rights #school #labt #vocation';
$meta_description = 'dd april dme disability hhs hse homework notes project professional resources revisit rights school labt vocation  --- Author:: April Cyr Date:: 11/18...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'dd #april #dme #disability #hhs #hse #homework #notes #project #professional #resources #revisit #rights #school #labt #vocation',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'dd april dme disability hhs hse homework notes project professional resources revisit rights school labt vocation  --- Author:: April Cyr Date:: 11/18...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\Accommodated Employment Primer.md',
);

// Post content
$post_content = '<p>#dd #april #dme #disability #hhs #hse #homework #notes #project #professional #resources #revisit #rights #school #labt #vocation</p>
<p>---
Author:: April Cyr
Date:: 11/18/2022
Index:: Public</p>
<p>---</p>

<h2>Post Traumatic Stress Disorder</h2>
PTSD is increasingly common in the days that we live in. PTSD is not only a condition that happens to the military. It is increasingly commonly documented among members of marginalized groups for ways they are treated.
<p>Accommodating PTSD does not always mean walking on egg shells around the person. Someone with extreme traumatic stress may be conditioned into Hypervigilance. Hypervigilant workers may be hyperproductive at the expense of their wellbeing. They may accomplish the completion of a task much quicker than it would take someone else much longer. This can result in pay gaps and work inequities. Giving someone with PTSD control over the conditions of their environment and work tasks is very helpful. In general, allowing negotiation over contract terms with workers results in more satisfied and productive workers.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>