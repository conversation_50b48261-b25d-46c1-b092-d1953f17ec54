<?php
// Auto-generated blog post
// Source: content\kitchen\ingredient-wishlist.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Wishlist for High-Quality Ingredients';
$meta_description = 'Wishlist for High-Quality Ingredients Pantry Staples: - Cooking Oils: Extra virgin olive oil, avocado oil, toasted sesame oil - Vinegars: Balsamic v...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Wishlist for High-Quality Ingredients',
  'author' => 'A. A. Chips',
  'date' => '2025-07-27',
  'excerpt' => 'Wishlist for High-Quality Ingredients Pantry Staples: - Cooking Oils: Extra virgin olive oil, avocado oil, toasted sesame oil - Vinegars: Balsamic v...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\kitchen\\ingredient-wishlist.md',
);

// Post content
$post_content = '<h1>Wishlist for High-Quality Ingredients</h1>
<h2>Pantry Staples:</h2>
    <ul>
        <li><strong>Cooking Oils:</strong> Extra virgin olive oil, avocado oil, toasted sesame oil</li>
        <li><strong>Vinegars:</strong> Balsamic vinegar, apple cider vinegar, red wine vinegar</li>
        <li><strong>Non-dairy Milks:</strong> Oat milk, almond milk, coconut milk</li>
        <li><strong>Beans:</strong> Black beans, pinto beans, kidney beans, chickpeas (garbanzo beans)</li>
        <li><strong>Rice:</strong> Basmati rice, jasmine rice, brown rice</li>
        <li><strong>Pasta:</strong> Whole wheat pasta, brown rice pasta, lentil pasta</li>
        <li><strong>Grains:</strong> Quinoa, barley, farro</li>
        <li><strong>Nuts & Seeds:</strong> Almonds, walnuts, cashews, flax seeds, chia seeds</li>
        <li><strong>Dried Fruits:</strong> Cranberries, raisins, cherries, chopped dates</li>
        <li><strong>Spices:</strong> Smoked paprika, cumin, cayenne pepper, turmeric, curry powder, dried herbs (thyme, oregano, rosemary)</li>
        <li><strong>Sweeteners:</strong> Maple syrup, honey, agave nectar</li>
    </ul>
<h2>Fresh Produce:</h2>
    <ul>
        <li><strong>Seasonal Fruits:</strong> Apples, pears, citrus fruits (oranges, grapefruits, clementines), berries (strawberries, blueberries, raspberries)</li>
        <li><strong>Seasonal Greens:</strong> Mixed greens, spinach, kale, arugula, swiss chard, herbs (fresh basil, parsley, cilantro, mint)</li>
        <li><strong>Vegetables:</strong> Bell peppers, onions, carrots, broccoli, cauliflower, sweet potatoes, mushrooms, tomatoes (in season)</li>
        <li><strong>Aromatics:</strong> Garlic, ginger, shallots</li>
    </ul>
<h2>Other:</h2>
    <ul>
        <li><strong>Beverages:</strong> Coffee beans, loose leaf tea, sparkling water, kombucha (optional)</li>
        <li><strong>Baking essentials:</strong> Flour (all-purpose, whole wheat, gluten-free if needed), sugar, baking powder, baking soda, cocoa powder</li>
        <li><strong>Plating Garnishes:</strong> Edible flowers (violets, pansies, nasturtiums), microgreens, citrus zest, toasted nuts & seeds</li>
    </ul>
<h2>Optional High-End Additions:</h2>
    <ul>
        <li>Specialty cooking oils: Truffle oil, walnut oil</li>
        <li>Aged balsamic vinegar</li>
        <li>Specialty cheeses (goat cheese, blue cheese)</li>
        <li>Locally sourced honey or maple syrup</li>
        <li>Fresh herbs (basil, thyme, rosemary) in pots</li>
        <li>Local, artisanal breads and crackers</li>
    </ul>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>