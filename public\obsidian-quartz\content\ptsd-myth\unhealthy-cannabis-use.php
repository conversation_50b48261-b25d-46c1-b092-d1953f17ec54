<?php
// Auto-generated blog post
// Source: content\ptsd-myth\unhealthy-cannabis-use.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Am I using cannabis to cope in unhealthy ways?';
$meta_description = 'Am I using cannabis to cope in unhealthy ways? Sometimes cannabis may be really helpful for alleviation of symptoms related to traumatic stress. Somet...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Am I using cannabis to cope in unhealthy ways?',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'Am I using cannabis to cope in unhealthy ways? Sometimes cannabis may be really helpful for alleviation of symptoms related to traumatic stress. Somet...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\unhealthy-cannabis-use.md',
);

// Post content
$post_content = '<h2>Am I using cannabis to cope in unhealthy ways?</h2>
<p>Sometimes cannabis may be really helpful for alleviation of symptoms related to traumatic stress. Sometimes it may be keeping us in unhealthy patterns and more of a vice in an addiction. Being open to taking breaks to evaluate recovery status is a good thing. Many of the compounds in cannabis, after consumed, store in fat cells in our body, for weeks, sometimes over a month. If we are using cannabis more than once a week over a long period of time, it’s likely building up in your system. While that may not result in a poisoning or traditional drug overdose, it can lead to a lot of problems, from lethargy, fatigue, to sleep disturbances, brain fog, executive dysfunction, mood swings, psychosis, disassociation, and much more. Sometimes the benefits are diminishing when tolerance builds.</p>
<p>Here are several well-formed questions, for regular users of cannabis to self-reflect on, to determine if cannabis is helpful or harmful:</p>
<h3>Is Cannabis consistently helpful in alleviating your symptoms of traumatic stress? [Effectiveness]</h3>
<h3>Has Cannabis use prevented the need for more expensive, invasive, and risky interventions? [Prevention]</h3>
<h3>In what ways might your day to day functioning impaired by using Cannabis medicinally/medically?[Assessment]</h3>
<h3>What is the frequency and intensity of using Cannabis? Is it weekly? Daily? Multiple times a day? Is the dosage small, or until you cough? [Description]</h3>

<h3>If you continue on this regimen, do you see your condition improving or worsening a year from now? [Prediction]</h3>
<h3>Does self-medicating with cannabis lead you to engage in high-risk activities such as reckless driving, neglecting responsibilities, making poor legal, financial, or health decisions, or anything that could come back to bite you later? [Risk]</h3>
<h3>Are there negative effects brought on by self-medicating with cannabis for traumatic stress? Are any aspects of this use hurting you or people you love? [Harm]</h3>
<h3>When you look at the positive outcomes, paired with the negative outcomes of use, do you find that it is a good idea to self-medicate with cannabis for traumatic stress? Would it be worthwhile to take a break?[Cost-Benefit]</h3>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>