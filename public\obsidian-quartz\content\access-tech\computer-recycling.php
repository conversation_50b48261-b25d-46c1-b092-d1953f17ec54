<?php
// Auto-generated blog post
// Source: content\access-tech\computer-recycling.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Computer Recycling - How Does it Work?';
$meta_description = 'February 26, 2019https://info.mayeralloys.com/ewaste-blog/computer-recycling-how-does-it-work  Ilene Lubellhttps://info.mayeralloys.com/ewaste-blog/...';
$meta_keywords = 'A. A. Chips, blog, #tech #articles #resources #climate #library';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Computer Recycling - How Does it Work?',
  'author' => 'Ilene Lubell',
  'date' => '2025-10-09',
  'excerpt' => 'February 26, 2019https://info.mayeralloys.com/ewaste-blog/computer-recycling-how-does-it-work  Ilene Lubellhttps://info.mayeralloys.com/ewaste-blog/...',
  'tags' => 
  array (
    0 => '#tech #articles #resources #climate #library',
  ),
  'source_file' => 'content\\access-tech\\computer-recycling.md',
);

// Post content
$post_content = '<p><a href="https://info.mayeralloys.com/ewaste-blog/computer-recycling-how-does-it-work" class="external-link">February 26, 2019</a>  <a href="https://info.mayeralloys.com/ewaste-blog/author/ilene-lubell" class="external-link">Ilene Lubell</a></p>
<h1>Computer Recycling: How Does it Work?</h1>
<p>Have you ever wondered what happens to your computer when it\'s recycled?</p>
<p>Tossing your old computer in the trash may help to reduce clutter, but can be problematic for a number of reasons, such as filling up landfills and risking data breaches. This doesn\'t mean you have to use your computer as a super-sized paperweight, though.![](https://info.mayeralloys.com/hubfs/Computer%20Recycling%20How%20Does%20it%20Work%3F.gif)You are right if you think your old computer isn\'t worth much, but the magic of recycling is that when you combine hundreds of components that are not worth much on their own, you can create something valuable enough to live a second or even third life.</p>
<p>You may already know that recycling is the best (and most responsible) way to dispose of your old computer equipment. What you may not know is how it works and the interesting uses for the materials that come from recycling your old computer.</p>
<p>Nearly every part of your computer can be recycled, including glass, plastic, metal, and circuit board components. For example, your computer\'s circuit board contains silver and copper, and the microprocessor is laced with gold. This isn\'t just to make your circuit board look pretty. These metals are good at conducting electrical signals, and also happen to be in relatively short supply world-wide. When computers are recycled, these metals are sent to a plant to be extracted, processed and refined, and then used again in new applications.</p>
<p>The amount of metal in a single computer is tiny, paper-thin, difficult to extract, and not of much value. But if you are able to recycle hundreds or thousands of computers at a time, and set up an efficient and safe process for automating the extraction of these metals, then it starts to make sense economically to combine and reuse these metals.</p>
<h3><strong>What Actually Happens to Your Old Computer When it’s Recycled</strong></h3>
<p>First, the outer casings are taken off, and the cables are unplugged. Microprocessors are removed. Just like they sound, microprocessors are very small, but don\'t let their size fool you. They are actually the easiest computer part to recycle since the only metal contained in them is (a very small amount of) gold. A mix of hydrochloric acid and nitric acid is used to separate the gold from the ceramics. Hundreds at a time are dissolved in a large barrel. What remains is pure gold dust. The extracted gold is then melted into gold bullion. Pretty cool, huh?</p>
<p>!<a href="https://info.mayeralloys.com/hs-fs/hubfs/Computer%20Recycling%20How%20Does%20it%20Work%3F.jpeg?width=300&name=Computer%20Recycling%20How%20Does%20it%20Work%3F.jpeg" class="external-link">Golden metallic plate</a></p>
<p>Other parts of the computer\'s interior are harder to recycle since they contain a mix of metals, but the effort is still well worthwhile when you have hundreds of computers. The metal parts are stripped down and put into a furnace. They are melted into a liquid mix of gold, silver and copper, which is poured out, cooled, pressed, and then sliced into plates. Each plate is placed into a bath and given a positive charge. The plates are then exposed to a negatively-charged copper plate to attract the copper particles, which loosen from the mixed metal plate, leaving just silver and gold.</p>
<h3><strong>What Can Be Done with the Metals in Your Computer?</strong></h3>
<p>In the next step, the plate is run through another bath where a steel plate attracts the silver forming it into crystals. After the second bath, the plate contains nothing but gold. Gold plates can be melted down and shaped into different forms that have a variety of uses. For example, they can be pinched into smaller plates that will become dental crowns. What once carried information around a computer\'s memory will shortly find a home in someone\'s mouth.</p>
<p>Gold plates can also be used to make "gold salt", which adds a little "seasoning" in the computer manufacturing process. Seriously, though, gold salt really is used in the manufacturing of computers. To create gold salt crystals from gold plates, they have to use cyanide, which is highly poisonous. The engineer makes sure that the container is sealed very tightly during the process. This salt may not be so good for fish and chips, but it is excellent for computer chips. A bucket of gold salt has a value of almost $60,000. The number of computers needed to make one bucket of gold salt, though, probably numbers in the thousands.</p>
<p>Other uses for gold plates include gold rods treated in the furnace. They\'re compressed and twisted into a coil, which is then transformed into fine wire used in computer\'s circuitry.</p>
<p>Another industry that has a huge demand for gold is the space industry. Satellites and vehicles such as the space shuttle use gold in their engines. Everyone knows gold is a precious metal, but who would have thought that it would have so many industrial uses?</p>
<p>Reusing reclaimed gold and other precious metals means there is less mining that has to be done. Recycling is also less costly than mining, therefore having a positive effect on the economy, as well the environment.</p>
<p>Companies that replace computers frequently - such as hospitals, universities, and insurance companies - have an even greater responsibility than individuals to ensure their computers are recycled appropriately. For this reason, many environmentally-conscience companies develop long-term relationships with trustworthy recycling companies that can responsibly manage large-volume drop-offs on a regular basis.</p>
<p>Companies that deal with confidential or proprietary data need to be especially careful to choose a recycling partner that can assure their data is kept secure, and then completely wiped from existence during the recycling process, while also ensuring all the reusable parts of their computers are put to optimal use.</p>
<p>Mayer Alloys Corporation is an R2 compliant provider in partnership with OmniSource Electronic Recycling, an R2 Certified Recycler. Mayer will provide you with peace of mind that you are disposing of your organization’s electronic waste safely and responsibly. All electronic waste is recycled in an R2 Certified facility. All hard drives are destroyed and Certificates of Destruction in compliance with Department of Defense (DoD) security standards are provided. For more information about electronic recycling check out our <a href="https://www.mayeralloys.com/ultimate-guide-to-corporate-electronic-recycling/" class="external-link"><strong>Ultimate Guide To Corporate Electronic Recycling</strong></a><strong>.</strong></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>