<?php
// Auto-generated blog post
// Source: content\street\The Swannanoa Mulch Fire.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'If people outside this area wonder why I post about Hurricame <PERSON>e, think about this.';
$meta_description = 'If people outside this area wonder why I post about Hurricame Helene, think about this.   WLOS ABC 13 reported today that 500,000 cubic yards of debri...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'If people outside this area wonder why I post about Hurricame Helene, think about this.',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => 'If people outside this area wonder why I post about Hurricame Helene, think about this.   WLOS ABC 13 reported today that 500,000 cubic yards of debri...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\street\\The Swannanoa Mulch Fire.md',
);

// Post content
$post_content = '<p>If people outside this area wonder why I post about Hurricame Helene, think about this.  
WLOS ABC 13 reported today that 500,000 cubic yards of debris have been removed in Buncombe County/Asheville NC in 11 weeks since <a href="https://www.facebook.com/hashtag/hurricanehelene?__eep__=6&__cft__[0]=AZX2CrJLZXCRxnlOWoDn3bmgXXaykK4GvToSUqKwBWuMDfskpWfM8-U6p5dX0muppAyAy-FdXJu991PNwDYUYNDPNVTfCB9y9pu669VT13-IG9psVmvBcPiVd_v6MbTRMjoWabsAFXga1WR_-N48WmKrNnkB31yf1vubWHcg6SyRBceR7cSlQlwDaXUPTYn_DJVpJD-9bFjqoPuW56rQBC00iQv9_HNvmyaoripmnXawv-p1bSBeM43vEuymnfYy_eY&__tn__=<em>NK</em>F" class="external-link">#HurricaneHelene</a>.  
That\'s an estimated FIVE PERCENT of the total debris left by the storm. At this pace. It will take SIX AND A HALF YEARS to finish the debris removal.  
This is in just 1 county out of the 9 counties hit hard by the storm and the national news has already forgotten about us.  
<a href="https://www.facebook.com/hashtag/prayforwesternnorthcarolina?__eep__=6&__cft__[0]=AZX2CrJLZXCRxnlOWoDn3bmgXXaykK4GvToSUqKwBWuMDfskpWfM8-U6p5dX0muppAyAy-FdXJu991PNwDYUYNDPNVTfCB9y9pu669VT13-IG9psVmvBcPiVd_v6MbTRMjoWabsAFXga1WR_-N48WmKrNnkB31yf1vubWHcg6SyRBceR7cSlQlwDaXUPTYn_DJVpJD-9bFjqoPuW56rQBC00iQv9_HNvmyaoripmnXawv-p1bSBeM43vEuymnfYy_eY&__tn__=<em>NK</em>F" class="external-link">#PrayForWesternNorthCarolina</a> </p>
<p>See less</p>
<p>— in <strong><a href="https://www.facebook.com/Asheville-North-Carolina-104063499628686/?__cft__[0]=AZX2CrJLZXCRxnlOWoDn3bmgXXaykK4GvToSUqKwBWuMDfskpWfM8-U6p5dX0muppAyAy-FdXJu991PNwDYUYNDPNVTfCB9y9pu669VT13-IG9psVmvBcPiVd_v6MbTRMjoWabsAFXga1WR_-N48WmKrNnkB31yf1vubWHcg6SyRBceR7cSlQlwDaXUPTYn_DJVpJD-9bFjqoPuW56rQBC00iQv9_HNvmyaoripmnXawv-p1bSBeM43vEuymnfYy_eY&__tn__=kC*F" class="external-link">Asheville</a></strong>.</p>
<p>"To help address needs after Helene caused extensive damage to Swannanoa\'s grocery store, Buncombe County is opening a new Community Engagement Market to help fill the gap and support our community with access to fresh, healthy food—all for free.</p>
<p>Additionally, Buncombe County Health and Human Services Department’s mobile health unit will also be there offering wellness checks, free vaccinations, and more. The Swannanoa market will be held on the first Monday of the month from 4:30-6 p.m. outdoors at the Asheville Christian Academy.</p>
<p>Grand Opening: Monday, April 7 | 4:30–6 p.m. Asheville Christian Academy gravel lot – 74 Riverwood Road, Swannanoa</p>
<p>Come shop for free, connect with your neighbors, and be part of something rooted in care and resilience.</p>
<p>Community Engagement Markets are open to all and held across Buncombe County.</p>
<p>(Note: Dates and times may vary due to weather or holidays.)"</p>
<p>I don\'t like letting my family live in my head rent-free.</p>
<p>We\'re experiencing wildfires all over from all the brush from the storm. It\'s very dry here and we only started to get rain again. If I die in a fire or lose where I live.</p>



<p>```cardlink
url: https://wlos.com/news/local/swannanoa-river-road-reopen-after-six-month-closure-from-hurricane-helene-damage-north-carolina-department-of-transportation-ncdot-debris
title: "Swannanoa River Road reopens Friday after 6-month closure from Helene damage"
description: "Swannanoa River Road is expected to reopen on the afternoon of Friday, April 4, according to the North Carolina Department of Transportation (NCDOT)."
host: wlos.com
favicon: https://wlos.com/resources/assets/wlos/images/logos/favicon-32x32.png
image: https://wlos.com/resources/media2/16x9/1280/986/center/90/641861db-2cfb-474c-8cf7-87decbcda87b-AshevilleTunnelRoadTrafficPackage.movframe3314.png
```
<a href="https://wlos.com/news/local/swannanoa-river-road-reopen-after-six-month-closure-from-hurricane-helene-damage-north-carolina-department-of-transportation-ncdot-debris" class="external-link">Swannanoa River Road reopens Friday after 6-month closure from Helene damage</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>