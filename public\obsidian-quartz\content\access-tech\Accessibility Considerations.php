<?php
// Auto-generated blog post
// Source: content\access-tech\Accessibility Considerations.md

// Load path helper and configuration
// Try multiple possible locations for path-helper.php
if (file_exists(__DIR__ . '/../path-helper.php')) {
    require_once __DIR__ . '/../path-helper.php';
    $config = include __DIR__ . '/../config.php';
} elseif (file_exists(__DIR__ . '/../../path-helper.php')) {
    require_once __DIR__ . '/../../path-helper.php';
    $config = include __DIR__ . '/../../config.php';
} elseif (file_exists(__DIR__ . '/../../../path-helper.php')) {
    require_once __DIR__ . '/../../../path-helper.php';
    $config = include __DIR__ . '/../../../config.php';
} else {
    die('Could not find path-helper.php. File: ' . __FILE__ . ', Dir: ' . __DIR__);
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Visual, Auditory, & Speech Impairments';
$meta_description = '!Pasted image 20220610100952.png Image source: Udemy.com Learn HTML and CSS from Scratch - Build a Responsive Website. Online Course by Himanshu Verma...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Visual, Auditory, & Speech Impairments',
  'author' => 'A. A. Chips',
  'date' => '2025-10-09',
  'excerpt' => '!Pasted image 20220610100952.png Image source: Udemy.com Learn HTML and CSS from Scratch - Build a Responsive Website. Online Course by Himanshu Verma...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\access-tech\\Accessibility Considerations.md',
);

// Post content
$post_content = '<p>**</p>
<p>![[Pasted image 20220610100952.png]]</p>
<p><em>*Image source: Udemy.com Learn HTML and CSS from Scratch - Build a Responsive Website. Online Course by Himanshu Verma.</em> </p>
<p>**## Divergent Learning Experiences</p>
<p>According to the CDC, one in four, or 26% of the population, lives with a disability. Many who do not call themselves disabled still have relevant impairment issues. Someone with visual astigmatism may wear glasses but not consider themselves disabled. Impairments like this may exist in many different ways, and learning environments must be geared toward accessibility. In addition to being the right thing to do, it is also a legal compliance mandate under the Americans with Disabilities Act. </p>

<p>Disability Impacts All of Us Infographic | CDC</p>
<p><a href="https://www.cdc.gov/ncbddd/disabilityandhealth/infographic-disability-impacts-all.html#:~:text=26%20percent%20%28one%20in%204%29%20of%20adults%20in,living%20with%20disabilities%20is%20highest%20in%20the%20South." class="external-link">https://www.cdc.gov/ncbddd/disabilityandhealth/infographic-disability-impacts-all.html</a></p>

<p>!<a href="https://lh4.googleusercontent.com/4mqAgi2EqMWiRz2HpQKO4otfk7rcVrKLJExAt0eVW4dTtN1d-B1cwUjkfOvLYM3JvMWKO_8wJBb6CJEuE4M0RG8ERqyBpZTORCYtA0_ZHGZYiwyde8MTmjawtuIO5f56nFwd3rkvPHHDw6ba9byJ7aTRiFho-MueGXie2HcTE6e4821zR7CtYcNwSd60yg" class="external-link">Disability Impacts ALL of US. 61 million adults in the United States live with a disability.</a></p>

<h3>Visual, Auditory, & Speech Impairments</h3>
<p>Visual Impairments can include blindness, light sensitivity, near/far-sightedness, and many others. Auditory impairments can include deafness, mixed hearing loss, auditory sensitivities, and painful conditions like Tinnitus. Speech disorders in adults are very common and can include issues rooted in anxiety, brain injuries, and medical conditions. Some cognitive impairments can impact processing processes, such as with Autism Spectrum Disorders. Sometimes impairments are related to environment. Listening along while in a coffee shop. Speaking based participation in a library. Maintaining visual focus on a smart phone while driving. These all are very common. </p>

<p>!<a href="https://lh5.googleusercontent.com/7nQU3hdQrCOQxWJreaObfYIzzIz-zlL8F1oU-bFvq7pSRPOhk0MoFl45SMTz1hCWAQ18-xVk0VrIhmvVTcpmAMFRqqViI_KAzzREMSl8CYv9HYIy6tToogj8rsiYk7M1vKEhGzrAGVJnBkChBlVaKdksN0obKnReJsMAsHQWAiDvxz0LLlDrBdeaywzY-A" class="external-link">See no evil, hear no evil, speak no evil.</a></p>

<p><a href="https://www.stronggo.com/blog/what-are-types-visual-impairments" class="external-link">What Are the Types of Visual Impairments? | TekWay | StrongGo - Tactile Warning Systems</a></p>

<p><a href="https://www.ldonline.org/ld-topics/processing-deficits/visual-and-auditory-processing-disorders" class="external-link">Visual and Auditory Processing Disorders | LD OnLine</a></p>

<h3>Sensory Processing</h3>
<p>Sensory processing may not impair someone’s ability to hear or see content physically but may make following along very uncomfortable. At a minimum, this detracts from learning, but at worst, this can cause pain and injury. An extreme and well known about this is epileptic seizures caused by flashing animations. Lesser known examples may be a phenomenon called Autistic Shutdown and Overload. Shutdown and Overload aren’t unique to Autism but are very common in Autistic People. </p>

<p><a href="https://www.autismtalkclub.com/what-does-an-autistic-shutdown-feel-like/#:~:text=Sensory%20overload%20is%20a%20common%20reason%20for%20a,intensely%2C%20sometimes%20to%20the%20point%20of%20physical%20pain." class="external-link">What Does An Autistic Shutdown Feel Like - AutismTalkClub.com</a></p>
<h3>Hypersensitivity vs. Hyposensitivity</h3>
<p>The Natural Variation in sensory experiences behaves like a Bell Curve. The Bell Curve is a concept in Statistics and Psychology that outlines Normal Distribution. For Hypo-Sensitive Individuals, you may need to dim lights in the room and provide quiet space. For Hyper-Sensitive Individuals, you may need very colorful, concise, and exciting content to capture their attention. This may look like having a worksheet as well as a video explaining the same materials. </p>

<p>#### Figure 1.1 Sensory Experience Distribution</p>

<p>![](https://lh3.googleusercontent.com/vo0r93KjpzhRYDgk2PUbl9RWJa2WPCUrNbmZXE5Wfrp0OmilvJHyQHyxim-oFzG6Shk-NcWJyjnIyTrXOzwjsD9HCnvaOUnbI3WKiBdMLCizDupNDphNMaGETzOhx8N_Mq1gM9z7gxIEyJejicJyeXXty8Bbdpr_VzYtx-ONDYF5XRIDF0SJkXvojzaesw)</p>
<p>Figure by April. Template from Wikimedia Commons.</p>

<h3>What are Synesthesia and Synesthetic Learning?</h3>
<p>Synesthesia is a sometimes desirable and sometimes unpleasant sensory experience that is sometimes an effect of mind-altering drugs. It is often described as ‘Seeing sounds’ or ‘tasting colors.’ It is a mixing of sensory experiences to achieve something unique. As an unpleasant experience, sometimes someone can hear the ringing of electrical appliances. As an ideal experience, it is making learning tools tap into multiple senses, making sense independently in more than one way.</p>

<p>Often structured video curriculums will have content length times next to each section. This is helpful as a structural guide. </p>

<p>Accessibility/Assistive Technology for Blind Users (keyboard navigation, screen readers, clear webpage hierarchy) are all trendy among all types of computer users because they improve the user experience.**</p>

<p> Additional Learning Resources for Various Types of Emergencies</p>
<p><strong>We recommend bookmarking this short guide to your phone so that you can access these tips in a pinch if you find yourself in one of these situations.</strong></p>

<p>In this course, we cover Mental Health First Aid. There are a number of emergencies we need more time and resources to delve into in this course, where a little bit of knowledge can go a long way. Here are several, along with short informational guides. Bookmarking these on a mobile device means that if you are a bystander in one of these situations, you can reference this in a pinch. In a genuine emergency, call 911 or local emergency services. However, these tips may prove helpful in the time it takes for help to dispatch. Any one of these could be their own two-day class:</p>

<p> How to respond to a Drug Overdose and interact with intoxicated people</p>

<p> How to respond to a seizure</p>

<p> How to administer an Epipen</p>
<p>Someone experiencing anaphylaxis may require an emergency shot administration right to their thigh. If you have Anaphylactic conditions, it may be a good idea to </p>

<p> Signs you or someone you care about may be in a violent relationship</p>
<p> Warnings and red flags of Human Trafficking to Spot</p>
<p>You may be the only safe person a trafficking victim can share that they are in danger with. Have the National Human Trafficking Hotline put as a contact on your phone in the event you ever need it in a pinch. Please do not endanger yourself by helping someone.</p>

<p> How to set effective boundaries as a helper</p>

<p> How to respond to threatening, aggressive, & belligerent behavior</p>
<p>Sometimes a person may become unhinged or act in an emergency. Do your best to refrain from engaging or engaging as minimally as possible. If talking is cooling them down, listen. Do not panic. </p>
<h3>Activity</h3>
<p>Think about someone in your life who may have either a Mental Illness, a Disability, or even a life-threatening reaction to shellfish. We will write out the text for a business card-sized communication aid. Something that they would benefit from having on them in the event of an emergency. You do not need to make an actual business card, but consider what 40 words may make a difference in an emergency if this card was shared.</p>
<p>**</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>